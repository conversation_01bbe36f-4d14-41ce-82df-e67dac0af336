plugins {
    alias(libs.plugins.android.application)
    alias(libs.plugins.kotlin.android)
    id("BlackObfuscatorPlugin")
}

android {
    namespace = "com.bearmod"
    compileSdk = 36
    lint {
        baseline = file("lint-baseline.xml")
    }
    defaultConfig {
        applicationId = "com.bearmod"
        minSdk = 28
        //noinspection OldTargetApi
        targetSdk = 35
        versionCode = 1
        versionName = "1.3"
        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
        ndk {
            abiFilters.add("arm64-v8a") // This tells <PERSON><PERSON><PERSON> to only build for arm64-v8a
        }
        multiDexEnabled = false

        // Development app configuration
        buildConfigField("String", "MUNDO_VERSION", "\"1.0.0\"")
        buildConfigField("String", "APP_TYPE", "\"DEVELOPMENT\"")
        buildConfigField("boolean", "DEBUG_TOOLS_ENABLED", "true")

        // BearMod v2.0 Configuration
        buildConfigField("boolean", "NONROOT_INJECTION_ENABLED", "true")
        buildConfigField("boolean", "ENHANCED_SECURITY_ENABLED", "true")
        buildConfigField("String", "BEARMOD_VERSION", "\"2.0\"")
    }

    // APK Signing Configuration
    signingConfigs {
        create("release") {
            val keystorePath = System.getenv("BEARMOD_KEYSTORE_PATH")
                ?: project.findProperty("BEARMOD_KEYSTORE_PATH") as String?
                ?: "C:\\Users\\<USER>\\BearOwner.jks"

            val keystoreFile = file(keystorePath)
            if (keystoreFile.exists()) {
                storeFile = keystoreFile
                storePassword = System.getenv("BEARMOD_KEYSTORE_PASSWORD")
                    ?: project.findProperty("BEARMOD_KEYSTORE_PASSWORD") as String?
                keyAlias = System.getenv("BEARMOD_KEY_ALIAS")
                    ?: project.findProperty("BEARMOD_KEY_ALIAS") as String?
                keyPassword = System.getenv("BEARMOD_KEY_PASSWORD")
                    ?: project.findProperty("BEARMOD_KEY_PASSWORD") as String?

                // Enable V1 and V2 signature schemes for maximum compatibility
                enableV1Signing = true
                enableV2Signing = true
                enableV3Signing = true
                enableV4Signing = true

                println("✅ BearMod signing configured with keystore: $keystorePath")
            } else {
                println("⚠️  Keystore not found at: $keystorePath")
                println("   Release builds will be unsigned. Please check keystore path.")
            }
        }

        // Debug signing config (uses default debug keystore)
        getByName("debug") {
            // Android Studio default debug keystore
            // This will be automatically used for debug builds
        }
    }

    buildTypes {
        debug {
            isDebuggable = true
            isJniDebuggable = true
            isMinifyEnabled = false
            // Use debug signing config (default Android debug keystore)
            signingConfig = signingConfigs.getByName("debug")
            // Development/debug configuration
            buildConfigField("boolean", "MUNDO_INTEGRATION", "true")
            buildConfigField("String", "BUILD_TYPE", "\"DEBUG\"")
        }
        release {
            isDebuggable = false
            isJniDebuggable = false
            isMinifyEnabled = true
            isShrinkResources = true
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro",
                "proguard-obfuscation.pro"
            )
            // Use release signing config (BearOwner keystore)
            signingConfig = signingConfigs.getByName("release")
            // Release configuration for testing
            buildConfigField("boolean", "MUNDO_INTEGRATION", "true")
            buildConfigField("String", "BUILD_TYPE", "\"RELEASE\"")

            // BearMod v2.0 Release Configuration
            buildConfigField("boolean", "NONROOT_INJECTION_ENABLED", "true")
            buildConfigField("boolean", "ENHANCED_SECURITY_ENABLED", "true")
            buildConfigField("String", "BEARMOD_VERSION", "\"2.0\"")
        }
    }
/*
    buildTypes {
        release {
            isMinifyEnabled = true
            isShrinkResources = true  // Enable resource shrinking
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
            signingConfig = signingConfigs.getByName("debug")
        }
        debug {
            isJniDebuggable = true
        }
    }
*/

    packaging {
        jniLibs {
            pickFirsts.addAll(
                listOf(
                    "**/libclient_static.so", //anti_hook (integrated into libbearmod.so)
                    "**/libc++_shared.so",
                    "**/libmundo.so" //mundo_core
                    // Removed: libhelper-*.so (Frida gadget replaced by ptrace-based injection)
                )
            )
            // Native libraries handled by mundo_core module dependency
        }
    }

    externalNativeBuild {
        ndkBuild {
            path = file("src/main/cpp/Android.mk")
        }
    }
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }
    ndkVersion = "27.1.12297006"
kotlinOptions {
    jvmTarget = "17"
}
    buildFeatures {
        viewBinding = true  // Enable ViewBinding
        buildConfig = true  // Enable BuildConfig generation
    }
    testOptions {
        unitTests {
            isIncludeAndroidResources = true
        }
    }
}

// ===================================================================
// BLACKOBFUSCATOR CONFIGURATION
// ===================================================================

blackObfuscator {
    enabled = true
    aggressiveMode = true
    preserveNativeMethods = true
    obfuscateStrings = true
    antiDebugging = true
    controlFlowObfuscation = true

    // Exclude critical packages from obfuscation
    excludePackages = listOf(
        "android",
        "androidx",
        "com.google",
        "okhttp3",
        "retrofit2"
    )

    // Preserve classes that must maintain their names for native interop
    preserveClasses = listOf(
        "com.bearmod.Launcher",
        "com.bearmod.MundoCore",
        "com.bearmod.GLES3JNIView",
        "com.bearmod.ImGui",
        "com.bearmod.plugin.NonRootManager"
    )
}

// ===================================================================
// AUTOMATED BUILD SYSTEM - APK NAMING & OBFUSCATION MANAGEMENT
// ===================================================================

import java.text.SimpleDateFormat
import java.util.*

// Automated APK naming and mapping file management
android.applicationVariants.configureEach { variant ->
    variant.outputs.configureEach { output ->
        val dateFormat = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.US)
        val timestamp = dateFormat.format(Date())
        val buildType = variant.buildType.name
        val versionName = variant.versionName
        val applicationId = variant.applicationId

        // Generate descriptive APK filename
        val fileName = "${applicationId}-${buildType}-v${versionName}-${timestamp}.apk"
        output.outputFileName = fileName

        // Create automated build archival system
        variant.assembleProvider.configure {
            doLast {
                val buildOutputDir = File(project.rootDir, "build-archive")
                val timestampedDir = File(buildOutputDir, "${buildType}-${timestamp}")

                // Create archive directory structure
                timestampedDir.mkdirs()

                // Copy APK to archive
                val apkFile = File(project.buildDir, "outputs/apk/${buildType}/${fileName}")
                if (apkFile.exists()) {
                    apkFile.copyTo(File(timestampedDir, fileName), overwrite = true)
                    println("✅ APK archived: ${timestampedDir.absolutePath}/${fileName}")
                }

                // Copy mapping files for release builds
                if (buildType == "release") {
                    val mappingDir = File(project.buildDir, "outputs/mapping/${buildType}")
                    val mappingFile = File(mappingDir, "mapping.txt")

                    if (mappingFile.exists()) {
                        val archivedMapping = File(timestampedDir, "mapping-${timestamp}.txt")
                        mappingFile.copyTo(archivedMapping, overwrite = true)
                        println("✅ Mapping file archived: ${archivedMapping.absolutePath}")

                        // Also copy to proguard directory for compatibility
                        val proguardDir = File(project.rootDir, "proguard")
                        proguardDir.mkdirs()
                        mappingFile.copyTo(File(proguardDir, "mapping-${timestamp}.txt"), overwrite = true)
                    }

                    // Copy R8 configuration dump if available
                    val configurationFile = File(mappingDir, "configuration.txt")
                    if (configurationFile.exists()) {
                        configurationFile.copyTo(File(timestampedDir, "r8-configuration-${timestamp}.txt"), overwrite = true)
                    }

                    // Copy seeds file if available
                    val seedsFile = File(mappingDir, "seeds.txt")
                    if (seedsFile.exists()) {
                        seedsFile.copyTo(File(timestampedDir, "seeds-${timestamp}.txt"), overwrite = true)
                    }

                    // Copy usage file if available
                    val usageFile = File(mappingDir, "usage.txt")
                    if (usageFile.exists()) {
                        usageFile.copyTo(File(timestampedDir, "usage-${timestamp}.txt"), overwrite = true)
                    }
                }

                // Generate build info file
                val buildInfoFile = File(timestampedDir, "build-info.txt")
                buildInfoFile.writeText("""
                    BearMod Build Information
                    ========================
                    Build Type: ${buildType}
                    Version: ${versionName}
                    Application ID: ${applicationId}
                    Build Time: ${Date()}
                    Timestamp: ${timestamp}
                    APK File: ${fileName}

                    Obfuscation: ${if (buildType == "release") "ENABLED" else "DISABLED"}
                    Debug Logging: ${if (buildType == "debug") "ENABLED" else "REMOVED"}

                    Native Libraries:
                    - libbearmod.so
                    - libmundo.so

                    Security Features:
                    - Code Obfuscation: ${if (buildType == "release") "✅" else "❌"}
                    - Resource Shrinking: ${if (buildType == "release") "✅" else "❌"}
                    - Debug Info Removal: ${if (buildType == "release") "✅" else "❌"}
                    - DevLogger Removal: ${if (buildType == "release") "✅" else "❌"}
                """.trimIndent())

                println("📋 Build info generated: ${buildInfoFile.absolutePath}")
                println("🏗️ Build ${buildType} completed successfully!")

                // Verify obfuscation for release builds
                if (buildType == "release") {
                    verifyObfuscation(timestampedDir, fileName)
                }
            }
        }
    }
}

// Obfuscation verification function
fun verifyObfuscation(buildDir: File, apkFileName: String) {
    try {
        println("🔍 Verifying obfuscation in release build...")

        val apkFile = File(buildDir, apkFileName)
        if (!apkFile.exists()) {
            println("⚠️ APK file not found for verification")
            return
        }

        // Check if mapping file exists (indicates obfuscation was applied)
        val mappingFiles = buildDir.listFiles { _, name -> name.startsWith("mapping-") }
        if (mappingFiles?.isNotEmpty() == true) {
            println("✅ Obfuscation mapping file found - obfuscation applied")
        } else {
            println("⚠️ No mapping file found - obfuscation may not be working")
        }

        // Additional verification could be added here
        // e.g., checking APK size, analyzing classes.dex, etc.

    } catch (e: Exception) {
        println("❌ Error during obfuscation verification: ${e.message}")
    }
}

// Task to clean old build archives (optional)
tasks.register("cleanOldBuilds") {
    group = "build"
    description = "Clean build archives older than 30 days"

    doLast {
        val buildOutputDir = File(project.rootDir, "build-archive")
        if (!buildOutputDir.exists()) return@doLast

        val thirtyDaysAgo = System.currentTimeMillis() - (30 * 24 * 60 * 60 * 1000L)
        var deletedCount = 0

        buildOutputDir.listFiles()?.forEach { dir ->
            if (dir.isDirectory && dir.lastModified() < thirtyDaysAgo) {
                dir.deleteRecursively()
                deletedCount++
            }
        }

        println("🧹 Cleaned $deletedCount old build directories")
    }
}

dependencies {
    // ===================================================================
    // Native Runtime Container Integration
    // ===================================================================
    implementation(project(":mundo_core"))

    implementation(libs.appcompat)
    implementation(libs.core)

    // Animation libraries
    implementation(libs.dynamicanimation)
    implementation(libs.interpolator)

    // Secure OTA Dependencies
    implementation("net.lingala.zip4j:zip4j:2.11.5") // For password-protected ZIP extraction
    implementation("com.squareup.okhttp3:okhttp:4.12.0") // HTTP client for KeyAuth integration

    // Networking dependencies for KeyAuth
    implementation(libs.retrofit)
    implementation(libs.converter.gson.v2110)
    implementation(libs.okhttp.v4120)
    implementation(libs.logging.interceptor.v4120)
    implementation(libs.gson)

    implementation ("com.squareup.okhttp3:okhttp:5.1.0")
    implementation ("com.squareup.okhttp3:logging-interceptor:5.1.0")
    implementation ("com.google.code.gson:gson:2.13.1")
    implementation ("org.json:json:20250517")

    // Security dependencies
    implementation (libs.security.crypto.v110beta01)
    implementation(libs.glide)

    implementation(libs.security.crypto)
    implementation(libs.core.ktx)

    implementation(libs.material)
    implementation(libs.constraintlayout)
    implementation(libs.constraintlayout.v214)// Use the latest stable version
    implementation(libs.navigation.fragment)

}

// ===================================================================
// app Module Configuration Summary
// ===================================================================
println("🛠️  app module configured:")
println("   → BearMod Development App (com.bearmod)")
println("   → Depends on mundo_core for libmundo.so testing")
println("   → Features: ESP system UE4, floating services, debug tools")
println("   → Build: Debug-enabled with JNI debugging and memory tracing")
println("   → Native: Android.mk build system with fixed UE4 functions")
