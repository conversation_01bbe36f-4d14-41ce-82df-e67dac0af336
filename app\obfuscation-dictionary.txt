# ===================================================================
# BEAR-INJECT OBFUSCATION DICTIONARY
# ===================================================================
# Custom dictionary for class, method, and field name obfuscation
# Uses misleading names to confuse reverse engineers

# Misleading class names (appear to be legitimate Android/system classes)
SystemService
ConfigManager
UtilityHelper
DataProcessor
NetworkHandler
SecurityProvider
CacheManager
ResourceLoader
ServiceBinder
ComponentFactory
ManagerImpl
HandlerService
ProcessorUtil
ProviderImpl
LoaderService
BinderUtil
FactoryImpl
ServiceUtil
ManagerService
HandlerImpl
ProcessorService
ProviderUtil
LoaderImpl
BinderService
FactoryUtil
ServiceImpl
ManagerUtil
HandlerService
ProcessorImpl
ProviderService
LoaderUtil
BinderImpl
FactoryService
ServiceProvider
ManagerLoader
HandlerUtil
ProcessorBinder
ProviderFactory
LoaderManager
BinderHandler
FactoryProcessor
ServiceLoader
ManagerBinder
HandlerFactory
ProcessorProvider
ProviderLoader
LoaderBinder
BinderFactory
FactoryManager
ServiceHandler
ManagerProcessor
HandlerProvider
ProcessorLoader
ProviderBinder
LoaderFactory
BinderManager
FactoryHandler
ServiceProcessor
ManagerProvider
HandlerLoader
ProcessorBinder
ProviderFactory
LoaderManager
BinderHandler
FactoryProcessor
ServiceLoader
ManagerBinder
HandlerFactory
ProcessorProvider
ProviderLoader
LoaderBinder
BinderFactory
FactoryManager
ServiceHandler
ManagerProcessor
HandlerProvider
ProcessorLoader
ProviderBinder
LoaderFactory
BinderManager
FactoryHandler
ServiceProcessor
ManagerProvider
HandlerLoader
ProcessorBinder
ProviderFactory
LoaderManager
BinderHandler
FactoryProcessor
ServiceLoader
ManagerBinder
HandlerFactory
ProcessorProvider
ProviderLoader
LoaderBinder
BinderFactory
FactoryManager
ServiceHandler
ManagerProcessor
HandlerProvider
ProcessorLoader
ProviderBinder
LoaderFactory
BinderManager
FactoryHandler
ServiceProcessor
ManagerProvider
HandlerLoader

# Misleading method names (appear to be standard operations)
initialize
configure
process
handle
manage
load
bind
create
destroy
start
stop
pause
resume
update
refresh
clear
reset
validate
verify
authenticate
authorize
encrypt
decrypt
encode
decode
compress
decompress
serialize
deserialize
parse
format
convert
transform
filter
sort
search
find
locate
retrieve
store
save
delete
remove
add
insert
append
prepend
replace
modify
edit
change
alter
adjust
calibrate
optimize
enhance
improve
upgrade
downgrade
migrate
backup
restore
sync
async
execute
invoke
call
run
perform
operate
function
method
procedure
routine
algorithm
calculation
computation
evaluation
assessment
analysis
synthesis
generation
production
construction
building
assembly
compilation
interpretation
translation
rendering
display
show
hide
reveal
conceal
expose
protect
secure
defend
guard
shield
block
allow
permit
deny
reject
accept
approve
disapprove
enable
disable
activate
deactivate
engage
disengage
connect
disconnect
link
unlink
attach
detach
mount
unmount
install
uninstall
setup
teardown
open
close
lock
unlock
seal
unseal
wrap
unwrap
pack
unpack
zip
unzip
tar
untar
archive
extract
import
export
upload
download
transmit
receive
send
deliver
dispatch
forward
redirect
route
navigate
browse
explore
discover
detect
identify
recognize
classify
categorize
organize
arrange
structure
format
layout
design
pattern
template
model
schema
framework
architecture
infrastructure
platform
environment
context
scope
domain
namespace
package
module
component
element
item
object
entity
instance
reference
pointer
handle
descriptor
identifier
token
key
value
pair
tuple
array
list
vector
matrix
table
map
dictionary
set
collection
container
wrapper
adapter
bridge
proxy
facade
decorator
observer
listener
callback
handler
processor
manager
service
provider
factory
builder
creator
generator
producer
consumer
publisher
subscriber
broadcaster
receiver
transmitter
sender
dispatcher
router
navigator
controller
coordinator
orchestrator
scheduler
timer
counter
accumulator
aggregator
collector
selector
filter
transformer
converter
formatter
parser
lexer
tokenizer
analyzer
validator
verifier
authenticator
authorizer
encryptor
decryptor
encoder
decoder
compressor
decompressor
serializer
deserializer
