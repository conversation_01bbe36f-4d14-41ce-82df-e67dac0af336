# ===================================================================
# BEAR-INJECT ADVANCED OBFUSCATION CONFIGURATION
# ===================================================================
# Purpose: Maximum obfuscation while preserving injection system functionality
# Target: Make classes.dex completely unreadable while maintaining native interop

# ===================================================================
# AGGRESSIVE OBFUSCATION SETTINGS
# ===================================================================

# Enable maximum obfuscation
-obfuscationdictionary obfuscation-dictionary.txt
-classobfuscationdictionary obfuscation-dictionary.txt
-packageobfuscationdictionary obfuscation-dictionary.txt

# Aggressive optimization passes
-optimizationpasses 7
-allowaccessmodification
-mergeinterfacesaggressively
-overloadaggressively
-repackageclasses ''
-flattenpackagehierarchy

# Remove all debugging information
-assumenosideeffects class android.util.Log {
    public static *** v(...);
    public static *** d(...);
    public static *** i(...);
    public static *** w(...);
    public static *** e(...);
    public static *** wtf(...);
    public static *** println(...);
    public static boolean isLoggable(...);
}

# ===================================================================
# DEVLOGGER COMPLETE REMOVAL (RELEASE BUILDS ONLY)
# ===================================================================

# Remove DevLogger completely from release builds
-assumenosideeffects class com.bearmod.debug.DevLogger {
    public static *** initialize(...);
    public static *** library(...);
    public static *** script(...);
    public static *** ota(...);
    public static *** injection(...);
    public static *** floating(...);
}

-assumenosideeffects class com.bearmod.debug.DevLogger$LibraryLogger {
    public *** loadAttempt(...);
    public *** loadSuccess(...);
    public *** loadFailure(...);
    public *** nativeMethodCall(...);
    public *** libraryInfo(...);
}

-assumenosideeffects class com.bearmod.debug.DevLogger$ScriptLogger {
    public *** executionStart(...);
    public *** executionSuccess(...);
    public *** executionFailure(...);
    public *** scriptLoaded(...);
    public *** secureScriptOperation(...);
}

-assumenosideeffects class com.bearmod.debug.DevLogger$OTALogger {
    public *** downloadStart(...);
    public *** downloadProgress(...);
    public *** downloadComplete(...);
    public *** downloadFailure(...);
    public *** checksumVerification(...);
    public *** cacheOperation(...);
    public *** keyAuthLibrary(...);
}

-assumenosideeffects class com.bearmod.debug.DevLogger$InjectionLogger {
    public *** mundoCoreInit(...);
    public *** injectionAttempt(...);
    public *** injectionSuccess(...);
    public *** injectionFailure(...);
    public *** injectionProgress(...);
    public *** keyAuthStatus(...);
    public *** hybridManagerStatus(...);
    public *** nonRootFallback(...);
    public *** securityFeature(...);
}

-assumenosideeffects class com.bearmod.debug.DevLogger$FloatingLogger {
    public *** overlayStart(...);
    public *** overlaySuccess(...);
    public *** overlayFailure(...);
    public *** permissionCheck(...);
    public *** imguiInit(...);
    public *** renderingStart(...);
    public *** renderingStop(...);
    public *** menuInteraction(...);
    public *** glesViewStatus(...);
}

-assumenosideeffects class com.bearmod.debug.DevLogger$Utils {
    public static *** logFileInfo(...);
    public static *** logMemoryUsage(...);
}

# Remove DevLoggerTest completely
-assumenosideeffects class com.bearmod.debug.DevLoggerTest {
    public static *** runAllTests(...);
    public static *** quickTest(...);
    private static *** test*(...);
}

# ===================================================================
# CRITICAL PRESERVATION RULES - INJECTION SYSTEM
# ===================================================================

# Preserve native method signatures and JNI interfaces
-keepclasseswithmembernames,includedescriptorclasses class * {
    native <methods>;
}

# Preserve classes accessed from native code (critical for injection)
-keep,allowobfuscation class com.bearmod.MundoCore {
    public <methods>;
    public <fields>;
}

-keep,allowobfuscation class com.bearmod.plugin.NonRootManager {
    public <methods>;
    public <fields>;
}

# Preserve GLES and ImGui classes (accessed from C++)
-keep,allowobfuscation class com.bearmod.GLES3JNIView {
    public <methods>;
    public <fields>;
}

-keep,allowobfuscation class com.bearmod.ImGui {
    public <methods>;
    public <fields>;
}

# Preserve Launcher class (entry point)
-keep class com.bearmod.Launcher {
    public static void main(java.lang.String[]);
    public <methods>;
}

# ===================================================================
# INJECTION SYSTEM OBFUSCATION RULES
# ===================================================================

# Obfuscate injection managers but preserve public interfaces
-keep,allowobfuscation class com.bearmod.injection.** {
    public <methods>;
}

-keep,allowobfuscation class com.bearmod.patch.** {
    public <methods>;
}

-keep,allowobfuscation class com.bearmod.security.** {
    public <methods>;
}

-keep,allowobfuscation class com.bearmod.ota.** {
    public <methods>;
}

# ===================================================================
# ANTI-REVERSE ENGINEERING MEASURES
# ===================================================================

# Remove source file names and line numbers
-renamesourcefileattribute ""
-keepattributes !SourceFile,!SourceDir,!LineNumberTable

# Remove parameter names
-keepattributes !LocalVariableTable,!LocalVariableTypeTable

# Keep only essential annotations
-keepattributes RuntimeVisibleAnnotations,RuntimeInvisibleAnnotations
-keepattributes RuntimeVisibleParameterAnnotations,RuntimeInvisibleParameterAnnotations
-keepattributes AnnotationDefault

# Remove unused classes aggressively
-dontwarn **
-ignorewarnings

# ===================================================================
# DEPENDENCY OBFUSCATION
# ===================================================================

# Obfuscate OkHttp/Retrofit while preserving functionality
-keep,allowobfuscation class okhttp3.** { *; }
-keep,allowobfuscation class retrofit2.** { *; }
-keep,allowobfuscation class com.google.gson.** { *; }

# ===================================================================
# STRING OBFUSCATION PREPARATION
# ===================================================================

# Mark string constants for obfuscation (requires additional tools)
-adaptclassstrings
-adaptresourcefilenames
-adaptresourcefilecontents

# ===================================================================
# FINAL VERIFICATION RULES
# ===================================================================

# Ensure critical functionality is preserved
-keep class com.bearmod.MainActivity {
    protected void onCreate(android.os.Bundle);
    public void onDestroy();
}

# Keep Application class if exists
-keep class com.bearmod.BearModApplication {
    public void onCreate();
}

# Preserve BuildConfig for build type detection
-keep class com.bearmod.BuildConfig {
    public static final boolean DEBUG;
    public static final String BUILD_TYPE;
}
