void RenderESP( AHUD *HUD/*, int ScreenWidth, int ScreenHeight*/) {
   

updateSkin();
    
     
        
    if (!g_Token.empty() && !g_Auth.empty() && g_Token == g_Auth) {
                    
    if (Config.Bypasssss) {
//写入D类(UE4+0x2478194,0);
写入D类(anogs+0x9C8A8,-763363328);
写入D类(anogs+0x98440,-698416192);
写入D类(anogs+0x5B6F54,16512);
写入D类(anogs+0x5B6F48,16512);
写入D类(anogs+0x5B6F20,16512);
写入D类(anogs+0x5B6320,16512);
写入D类(anogs+0x5B6F78,16512);
写入D类(anogs+0x5B7140,16512);
写入D类(anogs+0x5B7154,16512);
写入D类(anogs+0x5B715C,16512);
写入D类(anogs+0x5B7164,16512);
写入D类(anogs+0x5B7184,16512);
写入D类(anogs+0x5B7B24,16512);
写入D类(anogs+0x5B90A0,16512);
写入D类(anogs+0x5B90A4,16512);
写入D类(anogs+0x5B90C0,16512);
写入D类(anogs+0x5B90D0,16512);
写入D类(anogs+0x5B90E4,16512);
写入D类(anogs+0x5B912C,16512);
写入D类(anogs+0x5B94B0,16512);
写入D类(anogs+0x5B9548,16512);
写入D类(anogs+0x5B9558,16512);
写入D类(anogs+0x5B9D1C,16512);
写入D类(anogs+0x5B9D2C,16512);
写入D类(anogs+0x5B9D40,16512);
写入D类(anogs+0x5B9DB0,16512);
写入D类(anogs+0x5B9E48,16512);
写入D类(anogs+0x5B9F7C,16512);
写入D类(anogs+0x5B9F8C,16512);

 
//写入D类(UE4+0X8FFC77C,8.47963525e-21);
//写入D类(UE4+0X7D1DFC0,706675684);

写入D类(anogs+0x535DA8,-698416192);
写入D类(anogs+0x535E38,-698416192);
写入D类(anogs+0x535E58,-698416192);
写入D类(anogs+0x535E88,-698416192);
写入D类(anogs+0x535E98,-698416192);
写入D类(anogs+0x535EDC,-698416192);
写入D类(anogs+0x536150,-698416192);
写入D类(anogs+0x53630C,-698416192);
写入D类(anogs+0x536498,-698416192);
写入D类(anogs+0x536514,-698416192);
写入D类(anogs+0x536534,-698416192);
写入D类(anogs+0x536578,-698416192);
写入D类(anogs+0x5367E0,-698416192);
写入D类(anogs+0x536800,-698416192);
写入D类(anogs+0x536868,-698416192);
写入D类(anogs+0x536888,-698416192);
写入D类(anogs+0x5368E0,-698416192);
写入D类(anogs+0x536900,-698416192);
写入D类(anogs+0x53696C,-698416192);
写入D类(anogs+0x53698C,-698416192);
写入D类(anogs+0x5369E4,-698416192);
写入D类(anogs+0x536A04,-698416192);
写入D类(anogs+0x536A48,-698416192);
写入D类(anogs+0x536A68,-698416192);
写入D类(anogs+0x536C44,-698416192);
写入D类(anogs+0x536C64,-698416192);
写入D类(anogs+0x536E0C,-698416192);
写入D类(anogs+0x536EE4,-698416192);
写入D类(anogs+0x536F04,-698416192);
写入D类(anogs+0x536F80,-698416192);
写入D类(anogs+0x537058,-698416192);
写入D类(anogs+0x537268,-698416192);
}

                        auto GWorld = GetFullWorld();
    if (GWorld) {
        if (GWorld->PersistentLevel) {
            auto Actors = *(TArray<AActor *> *) ((uintptr_t) GWorld->PersistentLevel + Actors_Offset);
            //人数
                    
               //     fps.update();
        
                    
                    
                    
                    
                    UCharacterWeaponManagerComponent *WeaponManagerComponent;
ASTExtraShootWeapon * CurrentWeaponReplicated = 0;
AAvatarCapture* ALuaActor = 0;

UShootWeaponEntity *ShootWeaponEntityComp =0;

   ASTExtraPlayerCharacter *localPlayer = 0;
   ASTExtraPlayerController *localPlayerController = 0;
   ASTExtraPetCharacter *TEST = 0;
   APickUpListWrapperActor *wrapperactor  = 0;
   

   // screenWidth = ScreenWidth;
   // screenHeight = ScreenHeight;
                    
                         
    static float cnt = 0.0f;//@TEAMNRG1
              float r2 = cos(cnt) * .5f + .5f;//@TEAMNRG1
              float g2 = cos(cnt - 2.f * 3.14 / 3.f) * .5f + .5f;//@TEAMNRG1
              float b2 = cos(cnt - 4.f * 3.14 / 3.f) * .5f + .5f;//@TEAMNRG1
              if (cnt >= FLT_MAX) {//@TEAMNRG1
                  cnt = 0.0f;//@TEAMNRG1
              } else {//@TEAMNRG1
                  cnt += 0.02f;//@TEAMNRG1
              }    //@TEAMNRG1

#define COLOR_RGP FLinearColor(r2, g2, b2, 1.f)//@TEAMNRG1
 

      UCanvas *Canvas = HUD->Canvas;
      
      screenWidth = g_screenWidth;
        screenHeight = g_screenHeight;
      
    ScreenWidthNrg6 = Canvas->SizeX;
        ScreenHeightNrg6 = Canvas->SizeY;
        
    if (Canvas)
        
    {
        
  //        FPS = getF(getA(UE4 + 0xC2242F0));
        
        static bool loadFont = false;
        if (!loadFont)
        {
            pthread_t t;
            pthread_create(&t, 0, LoadFont, 0);
            loadFont = true;
        }

        if (!tslFont || !robotoTinyFont || !tslFontS || !itemfont)
            return;
         
            timer RenderingFPS;
    RenderingFPS.SetFps(Config.RunFPS);
    RenderingFPS.AotuFPS_init();
    RenderingFPS.setAffinity();
            /*
       {
        if (BypassLogo2_2) {
            
  
       tslFont->LegacyFontSize = 12;
        DrawText(HUD, BYPASS_GL_KR_VNG_TW, {105, (float) screenHeight - 84},  COLOR_WHITE);
     

          }
       }
       */
       

 
        UGameplayStatics *gGameplayStatics = (UGameplayStatics *)UGameplayStatics::StaticClass();
UKismetMathLibrary* UMC = (UKismetMathLibrary*)UKismetMathLibrary::StaticClass();
ULuaOverriderInterface* BOX = (ULuaOverriderInterface*)ULuaOverriderInterface::StaticClass();
UItemAvatarComponentBase* SKIN = (UItemAvatarComponentBase*)UItemAvatarComponentBase::StaticClass();
        UKismetSystemLibrary*USl = (UKismetSystemLibrary*)UKismetSystemLibrary::StaticClass();
        
        //auto NRGtestskin = ( UItemAvatarComponentBase *) pFunc;
        
        
        auto GWorld = GetFullWorld();
        if (GWorld) {
            UNetDriver *NetDriver = GWorld->NetDriver;
            if (NetDriver) {
                UNetConnection *ServerConnection = NetDriver->ServerConnection;
                if (ServerConnection) {
                    localPlayerController = (ASTExtraPlayerController *)ServerConnection->PlayerController;
                }
            }

            
            
            
           
            if (localPlayerController)
            {
                std::vector<ASTExtraPlayerCharacter *> PlayerCharacter;
                GetAllActors(PlayerCharacter);
    int IntCount = 0;
                for (auto actor = PlayerCharacter.begin(); actor != PlayerCharacter.end(); actor++) {
                    auto Actor = *actor;
                    if (Actor->PlayerKey == ((ASTExtraPlayerController *)localPlayerController)->PlayerKey) {
                        localPlayer = Actor;
                        break;
                    }
                }

                if (localPlayer)
                {
                    
                    
               if (Config.MemoryWideView){
                        localPlayer->ThirdPersonCameraComponent->SetFieldOfView(Config.MemoryWideViewSize);
                     //   localPlayer->ScopeCameraComp->SetFieldOfView(Config.MemoryWideViewSize); //
                   //    localPlayer->FPPCameraComp->SetFieldOfView(Config.MemoryWideViewSize - 50);
              }
                    
                    
          
                auto WeaponManagerComponent = localPlayer->WeaponManagerComponent;
    if (WeaponManagerComponent) 
    {
     auto Slot = WeaponManagerComponent->GetCurrentUsingPropSlot();
     if ((int) Slot.GetValue() >= 1 && (int) Slot.GetValue() <= 3) 
     {
      auto CurrentWeaponReplicated = (ASTExtraShootWeapon *) WeaponManagerComponent->CurrentWeaponReplicated;
      if (CurrentWeaponReplicated) 
      {
       auto ShootWeaponEntityComp = CurrentWeaponReplicated->ShootWeaponEntityComp;
       auto ShootWeaponEffectComp = CurrentWeaponReplicated->ShootWeaponEffectComp;
       if (ShootWeaponEntityComp && ShootWeaponEffectComp) 
       {
       
                             
          if (Config.SmallCrosshair){
    ShootWeaponEntityComp->GameDeviationFactor = NULL;
          }
          
            
          
          
       }
      }
     }
    }
if (Config.SkinOpen) {
if (Config.SkinEnable) {
 if (localPlayerController->BackpackComponent){
     auto data = localPlayerController->BackpackComponent->ItemListNet;
     auto bag = data.IncArray;
     for (int j = 0; j < bag.Num(); j++) {
     int ID = bag[j].Unit.DefineID.TypeSpecificID;

for (int i = 0; i < sizeof(bag333) / sizeof(bag333[0]); i++) {
                 if (ID == bag333[i]) {
                     bag[j].Unit.DefineID.TypeSpecificID = new_Skin.baglv3;
                     break;
                  }
                 }
                
for (int i = 0; i < sizeof(Helmet3) / sizeof(Helmet3[0]); i++) {
                 if (ID == Helmet3[i]) {
                     bag[j].Unit.DefineID.TypeSpecificID = new_Skin.helmetlv3;
                     break;
                  }
                 }
                
                
                 
                 
                
           
            }
        }
          }}
                    
                    
          
          
          if (Config.SkinOpen) {
if (Config.SkinEnable) {
if (localPlayer && localPlayer->AvatarComponent2) {
auto AvatarComp = localPlayer->AvatarComponent2;
FNetAvatarSyncData NetAvatarComp = *(FNetAvatarSyncData*)((uintptr_t)AvatarComp + 0x388);

auto Slotsybc = NetAvatarComp.SlotSyncData;

if (SuitSkinHackHudSdk == 1) {
Slotsybc[5].ItemId = 1405909; //Blood Raven X-Suit     
} else if (SuitSkinHackHudSdk == 2) {
Slotsybc[5].ItemId = 1405628; //Golden Pharaoh X-Suit          
} else if (SuitSkinHackHudSdk == 3) {
Slotsybc[5].ItemId = 1406152; //Avalanche X-suit        
} else if (SuitSkinHackHudSdk == 4) {
Slotsybc[5].ItemId = 1406475; //Irresidence X-suit     
} else if (SuitSkinHackHudSdk == 5) {
Slotsybc[5].ItemId = 1405983; //Poseidon X-suit 
} else if (SuitSkinHackHudSdk == 6) {
Slotsybc[5].ItemId = 1406638; //Arcane Jester X-Suit    
} else if (SuitSkinHackHudSdk == 7) {
Slotsybc[5].ItemId = 1406311; //Silvanus X-Suit  
} else if (SuitSkinHackHudSdk == 8) {
Slotsybc[5].ItemId = 1406971;//Marmoris X-Suit    
} else if (SuitSkinHackHudSdk == 9) {
Slotsybc[5].ItemId = 1407103; //Fiore X-Suit
} else if (SuitSkinHackHudSdk == 10) { 
Slotsybc[5].ItemId = 1405145; //Invader
} else if (SuitSkinHackHudSdk == 11) { 
Slotsybc[5].ItemId = 1400782;//Glacier Set
} else if (SuitSkinHackHudSdk == 12) { 
Slotsybc[5].ItemId = 1400119;//Wanderer Outfit
} else if (SuitSkinHackHudSdk == 13) { 
Slotsybc[5].ItemId = 1400117;//FireMan Set
} else if (SuitSkinHackHudSdk == 14) { 
Slotsybc[5].ItemId = 1400693;//Vampire Set
} else if (SuitSkinHackHudSdk == 15) { 
Slotsybc[5].ItemId = 1405092;//The Fool Set
} else if (SuitSkinHackHudSdk == 16) { 
Slotsybc[5].ItemId = 1405090;//Smooth Hitman Set (Cat) {
} else if (SuitSkinHackHudSdk == 17) { 
Slotsybc[5].ItemId = 1405482;//Anniversary Unicorn Set
} else if (SuitSkinHackHudSdk == 18) { 
Slotsybc[5].ItemId = 1405583;//Dazzling Youth Set
} else if (SuitSkinHackHudSdk == 19) { 
Slotsybc[5].ItemId = 1405593;//Red Commander Set
} else if (SuitSkinHackHudSdk == 20) { 
Slotsybc[5].ItemId = 1405629;//Dark Widow Set
} else if (SuitSkinHackHudSdk == 21) { 
Slotsybc[5].ItemId = 1405582;//Violet Halo Set
} else if (SuitSkinHackHudSdk == 22) { 
Slotsybc[5].ItemId = 1404049;
Slotsybc[6].ItemId = 1404050;
Slotsybc[7].ItemId = 1404051;
} else if (SuitSkinHackHudSdk == 23) { 
Slotsybc[5].ItemId = 1405623; //Yellow Mummy
} else if (SuitSkinHackHudSdk == 24) { 
Slotsybc[5].ItemId = 1400687; //White Mummy
} else if (SuitSkinHackHudSdk == 25) { 
Slotsybc[5].ItemId = 1405102; //Golden Trigger
} else if (SuitSkinHackHudSdk == 26) { 
Slotsybc[5].ItemId = 1405207; //Ryan Set
} else if (SuitSkinHackHudSdk == 27) { 
Slotsybc[5].ItemId = 1406398; //Flamewraith Set
} else if (SuitSkinHackHudSdk == 28) { 
Slotsybc[5].ItemId = 1406742; //Silver Guru - Set
}else if (SuitSkinHackHudSdk == 29) { 
Slotsybc[5].ItemId = 1407225; //luminous Set
}else if (SuitSkinHackHudSdk == 30) { 
Slotsybc[5].ItemId = 1407180; //
} else if (SuitSkinHackHudSdk == 31) { 
Slotsybc[5].ItemId = 1407800; //
} else if (SuitSkinHackHudSdk == 32) { 
Slotsybc[5].ItemId = 1407145; //
} else if (SuitSkinHackHudSdk == 33) { 
Slotsybc[5].ItemId = 80020002; //
} else if (SuitSkinHackHudSdk == 34) { 
Slotsybc[5].ItemId = 1407187; //
} else if (SuitSkinHackHudSdk == 35) { 
Slotsybc[5].ItemId = 1407229; //
} else if (SuitSkinHackHudSdk == 36) { 
Slotsybc[5].ItemId = 1407240; //
} else if (SuitSkinHackHudSdk == 37) { 
Slotsybc[5].ItemId = 1407222; //
} else if (SuitSkinHackHudSdk == 38) { 
Slotsybc[5].ItemId = 1407247; //
} else if (SuitSkinHackHudSdk == 39) { 
Slotsybc[5].ItemId = 1407186; //
} else if (SuitSkinHackHudSdk == 40) { 
Slotsybc[5].ItemId = 1407239; //
} else if (SuitSkinHackHudSdk == 41) { 
Slotsybc[5].ItemId = 1407232; //
} else if (SuitSkinHackHudSdk == 42) { 
Slotsybc[5].ItemId = 1407250; //
} else if (SuitSkinHackHudSdk == 43) { 
Slotsybc[5].ItemId = 1406657; //
} else if (SuitSkinHackHudSdk == 44) { 
Slotsybc[5].ItemId = 1406660; //
} else if (SuitSkinHackHudSdk == 45) { 
Slotsybc[5].ItemId = 1405186; //
} else if (SuitSkinHackHudSdk == 46) { 
Slotsybc[5].ItemId = 1405015; //
} else if (SuitSkinHackHudSdk == 47) { 
Slotsybc[5].ItemId = 1407160; //
} else if (SuitSkinHackHudSdk == 48) { 
Slotsybc[5].ItemId = 1407161; //
} else if (SuitSkinHackHudSdk == 49) { 
Slotsybc[5].ItemId = 1400690; //
} else if (SuitSkinHackHudSdk == 50) { 
Slotsybc[5].ItemId = 1405005; //
} else if (SuitSkinHackHudSdk == 51) { 
Slotsybc[5].ItemId = 1400692; //
} else if (SuitSkinHackHudSdk == 52) { 
Slotsybc[5].ItemId = 1400678; //
} else if (SuitSkinHackHudSdk == 53) { 
Slotsybc[5].ItemId = 1406658; //
} else if (SuitSkinHackHudSdk == 54) { 
Slotsybc[5].ItemId = 1405013; //
} else if (SuitSkinHackHudSdk == 55) { 
Slotsybc[5].ItemId = 1405004; //
} else if (SuitSkinHackHudSdk == 56) { 
Slotsybc[5].ItemId = 1400668; //
} else if (SuitSkinHackHudSdk == 57) { 
Slotsybc[5].ItemId = 1405006; //
}

//Slotsybc[8].ItemId = new_Skin.baglv3;
//Slotsybc[9].ItemId = new_Skin.helmetlv3;

if (helmetSkinHackHudSdk == 1)
Slotsybc[9].ItemId = new_Skin.helmetlv3 = 1502003028; //Scarlet Beast Helmet (Lv. 3)
if (helmetSkinHackHudSdk == 2)
Slotsybc[9].ItemId = new_Skin.helmetlv3 = 1502003014; //Inferno Rider Helmet (Lv. 3)
if (helmetSkinHackHudSdk == 3)
Slotsybc[9].ItemId = new_Skin.helmetlv3 = 1502003023; //Glacier Helmet (Lv. 3)
if (helmetSkinHackHudSdk == 4)
Slotsybc[9].ItemId = new_Skin.helmetlv3 = 1502003065; //Moon Bunny Helmet (Lv. 3)
if (helmetSkinHackHudSdk == 5)
Slotsybc[9].ItemId = new_Skin.helmetlv3 = 1502003031; //Mutated Helmet (Lv. 3)
if (helmetSkinHackHudSdk == 6)
Slotsybc[9].ItemId = new_Skin.helmetlv3 = 1502003033; //Intergalactic Helmet (Lv. 3)
if (helmetSkinHackHudSdk == 7)
Slotsybc[9].ItemId = new_Skin.helmetlv3 = 1502003069; //Masked Psychic Helmet (Lv. 3)
if (helmetSkinHackHudSdk == 8)
Slotsybc[9].ItemId = new_Skin.helmetlv3 = 1502003261; //Atlantic Tech Helmet (Lv. 3)
if (helmetSkinHackHudSdk == 9)
Slotsybc[9].ItemId = new_Skin.helmetlv3 = 1502003272; //Mystic Battle Helmet (Lv. 3)
if (helmetSkinHackHudSdk == 10)
Slotsybc[9].ItemId = new_Skin.helmetlv3 = 1502003183; //Godzilla Helmet (Lv. 3)



if (bagSkinHackHudSdk == 1)
Slotsybc[8].ItemId = new_Skin.baglv3 = 1501003220; //Blood Raven Backpack (Lv. 3)
if (bagSkinHackHudSdk == 2)
Slotsybc[8].ItemId = new_Skin.baglv3 = 1501003174; //Pharaoh's Regalia Backpack (Lv. 3)
if (bagSkinHackHudSdk == 3)
Slotsybc[8].ItemId = new_Skin.baglv3 = 1501003051; //The Fool Backpack (Lv. 3)
if (bagSkinHackHudSdk == 4)
Slotsybc[8].ItemId = new_Skin.baglv3 = 1501003443; //Luminous Galaxy Backpack (Lv. 3)
if (bagSkinHackHudSdk == 5)
Slotsybc[8].ItemId = new_Skin.baglv3 = 1501003265; //Poseidon Backpack (Lv. 3)
if (bagSkinHackHudSdk == 6)
Slotsybc[8].ItemId = new_Skin.baglv3 = 1501003321; //Gackt Moonsaga Backpack(Lv. 3)
if (bagSkinHackHudSdk == 7)
Slotsybc[8].ItemId = new_Skin.baglv3 = 1501003277; //Godzilla Backpack (Lv. 3)
if (bagSkinHackHudSdk == 8)
Slotsybc[8].ItemId = new_Skin.baglv3 = 1501003550; //Frosty Snowglobe Backpack (Lv. 3)
if (bagSkinHackHudSdk == 9)
Slotsybc[8].ItemId = new_Skin.baglv3 = 1501003552; //Ebil Bunny Backpack (Lv. 3)
 if (bagSkinHackHudSdk == 10)
Slotsybc[8].ItemId = new_Skin.baglv3 = 1501003061; //Godzilla Backpack (Lv. 3)




if (MainSuit != Slotsybc[5].ItemId) {
MainSuit = Slotsybc[5].ItemId;
localPlayer->AvatarComponent2->OnRep_BodySlotStateChanged();
}
if (SuitSkinHackHudSdk == 22) { 
if (MainSuit != Slotsybc[6].ItemId) {
MainSuit = Slotsybc[6].ItemId;
localPlayer->AvatarComponent2->OnRep_BodySlotStateChanged();
}
if (MainSuit != Slotsybc[7].ItemId) {
MainSuit = Slotsybc[7].ItemId;
localPlayer->AvatarComponent2->OnRep_BodySlotStateChanged();
}
}
}
}
}

        if (Config.SkinOpen) {
if (Config.SkinEnable){
     if (localPlayer->WeaponManagerComponent != 0) {
     if (localPlayer->WeaponManagerComponent->CurrentWeaponReplicated != 0 ) {
         int weapowep = localPlayer->WeaponManagerComponent->CurrentWeaponReplicated->GetWeaponID();
        auto currentTime = std::chrono::steady_clock::now();
         auto landchud = localPlayer->WeaponManagerComponent->CurrentWeaponReplicated->synData;
         auto timeDiff = std::chrono::duration_cast<std::chrono::milliseconds>(currentTime - lastWeaponChangeTime).count();
         if (timeDiff > 1000) {
         for (int j = 0; j < landchud.Num(); j++) {
             auto& weaponInfo = landchud[j];
             auto weaponid = weaponInfo.DefineID.TypeSpecificID;
             if (weaponid != 0)
             {
             
             
             
                 if (weapowep == 101001 || weapowep == 1010011 || weapowep == 1010012 || weapowep == 1010013 || weapowep == 1010014 || weapowep == 1010015 || weapowep == 1010016 || weapowep == 1010017){
                 for (int i = 0; i < sizeof(akmv) / sizeof(akmv[0]); i++) {
                 if (weaponid == akmv[i]) {
                     weaponInfo.DefineID.TypeSpecificID = new_Skin.AKM;
                     break;
                  }
                 }
                 for (int i = 0; i < sizeof(akmmag) / sizeof(akmmag[0]); i++) {
                 if (weaponid == akmmag[i]) {
                     weaponInfo.DefineID.TypeSpecificID = new_Skin.AKM_Mag;
                     break;
                  }
                 }
                }
              ///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
                if (weapowep == 103001 || weapowep == 1030011 || weapowep == 1030012 || weapowep == 1030013 || weapowep == 1030014 || weapowep == 1030015 || weapowep == 1030016 || weapowep == 1030017){
                 for (int i = 0; i < sizeof(kar) / sizeof(kar[0]); i++) {
                 if (weaponid == kar[i]) {
                     weaponInfo.DefineID.TypeSpecificID = new_Skin.K98;
                     break;
                  }
                 }
                }
                if (weapowep == 103002 || weapowep == 1030021 || weapowep == 1030022 || weapowep == 1030023 || weapowep == 1030024 || weapowep == 1030025 || weapowep == 1030026 || weapowep == 1030027){
                 for (int i = 0; i < sizeof(m24) / sizeof(m24[0]); i++) {
                 if (weaponid == m24[i]) {
                     weaponInfo.DefineID.TypeSpecificID = new_Skin.M24;
                     break;
                  }
                 }
                }
              ///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
                if (weapowep == 103003 || weapowep == 1030031 || weapowep == 1030032 || weapowep == 1030033 || weapowep == 1030034 || weapowep == 1030035 || weapowep == 1030036 || weapowep == 1030037){
                 for (int i = 0; i < sizeof(awm) / sizeof(awm[0]); i++) {
                 if (weaponid == awm[i]) {
                     weaponInfo.DefineID.TypeSpecificID = new_Skin.AWM;
                     break;
                  }
                 }
                }
              ///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
              
                if (weapowep == 103012 || weapowep == 1030121 || weapowep == 1030122 || weapowep == 1030123 || weapowep == 1030124 || weapowep == 1030125 || weapowep == 1030126 || weapowep == 1030127){
                 for (int i = 0; i < sizeof(amr) / sizeof(amr[0]); i++) {
                 if (weaponid == amr[i]) {
                     weaponInfo.DefineID.TypeSpecificID = new_Skin.AMR;
                     break;
                  }
                 }
                }
              ///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
              if (weapowep == 103007 || weapowep == 1030071 || weapowep == 1030072 || weapowep == 1030073 || weapowep == 1030074 || weapowep == 1030075 || weapowep == 1030076 || weapowep == 1030077){
                 for (int i = 0; i < sizeof(mk14) / sizeof(mk14[0]); i++) {
                 if (weaponid == mk14[i]) {
                     weaponInfo.DefineID.TypeSpecificID = new_Skin.MK14;
                     break;
                  }
                 }
                }
                
              ///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
              
            
              
              
                if (weapowep == 101102 || weapowep == 1011021 || weapowep == 1011022 || weapowep == 1011023 || weapowep == 1011024 || weapowep == 1011025 || weapowep == 1011026 || weapowep == 1011027){
                 for (int i = 0; i < sizeof(ace32) / sizeof(ace32[0]); i++) {
                 if (weaponid == ace32[i]) {
                     weaponInfo.DefineID.TypeSpecificID = new_Skin.ACE32;
                     break;
                  }
                 }
                }
            ///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
                if (weapowep == 102003){
                 for (int i = 0; i < sizeof(vector2) / sizeof(vector2[0]); i++) {
                 if (weaponid == vector2[i]) {
                     weaponInfo.DefineID.TypeSpecificID = new_Skin.Vector;
                     break;
                  }
                 }
                }
                ///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
                if (weapowep == 102002){
                 for (int i = 0; i < sizeof(ump) / sizeof(ump[0]); i++) {
                 if (weaponid == ump[i]) {
                     weaponInfo.DefineID.TypeSpecificID = new_Skin.UMP;
                     break;
                  }
                 }
                }
                ///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
                if (weapowep == 102001){
                 for (int i = 0; i < sizeof(uzi) / sizeof(uzi[0]); i++) {
                 if (weaponid == uzi[i]) {
                     weaponInfo.DefineID.TypeSpecificID = new_Skin.UZI;
                     break;
                  }
                 }
                }
                ///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
                if (weapowep == 101002){
                 for (int i = 0; i < sizeof(m16) / sizeof(m16[0]); i++) {
                 if (weaponid == m16[i]) {
                     weaponInfo.DefineID.TypeSpecificID = new_Skin.M16A4;
                     break;
                  }
                 }
                 for (int i = 0; i < sizeof(m16s) / sizeof(m16s[0]); i++) {
                 if (weaponid == m16s[i]) {
                     weaponInfo.DefineID.TypeSpecificID = new_Skin.M16A4_Stock;
                     break;
                  }
                 }
                 for (int i = 0; i < sizeof(m16mag) / sizeof(m16mag[0]); i++) {
                 if (weaponid == m16mag[i]) {
                     weaponInfo.DefineID.TypeSpecificID = new_Skin.M16A4_Mag;
                     break;
                  }
                 }
                }
                ///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
                
                if (weapowep == 101006){
                 for (int i = 0; i < sizeof(aug) / sizeof(aug[0]); i++) {
                 if (weaponid == aug[i]) {
                     weaponInfo.DefineID.TypeSpecificID = new_Skin.AUG;
                     break;
                  }
                 }
                }
                    ///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
                if (weapowep == 101005){
                 for (int i = 0; i < sizeof(groza) / sizeof(groza[0]); i++) {
                 if (weaponid == groza[i]) {
                     weaponInfo.DefineID.TypeSpecificID = new_Skin.Groza;
                     break;
                  }
                 }
                }
                
            ///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////     
                if (weapowep == 105002){
                 for (int i = 0; i < sizeof(dp) / sizeof(dp[0]); i++) {
                 if (weaponid == dp[i]) {
                     weaponInfo.DefineID.TypeSpecificID = new_Skin.DP28;
                     break;
                  }
                 }
                }
                if (weapowep == 105001){
                 for (int i = 0; i < sizeof(m249) / sizeof(m249[0]); i++) {
                 if (weaponid == m249[i]) {
                     weaponInfo.DefineID.TypeSpecificID = new_Skin.M249;
                     break;
                  }
                 }
                }
                ///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////     
                
                    if (weapowep == 105010){
                 for (int i = 0; i < sizeof(mg3) / sizeof(mg3[0]); i++) {
                 if (weaponid == mg3[i]) {
                     weaponInfo.DefineID.TypeSpecificID = new_Skin.MG3;
                     break;
                  }
                 }
                }
                ///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////     
                
                
                
                
                if (weapowep == 101003){
                 for (int i = 0; i < sizeof(scar) / sizeof(scar[0]); i++) {
                 if (weaponid == scar[i]) {
                     weaponInfo.DefineID.TypeSpecificID = new_Skin.Scar;
                     break;
                  }
                 }
                 for (int i = 0; i < sizeof(scarmag) / sizeof(scarmag[0]); i++) {
                 if (weaponid == scarmag[i]) {
                     weaponInfo.DefineID.TypeSpecificID = new_Skin.Scar_Mag;
                     break;
                  }
                 }
                }
        ///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////     
                if (weapowep == 101008){
                 for (int i = 0; i < sizeof(m7) / sizeof(m7[0]); i++) {
                 if (weaponid == m7[i]) {
                     weaponInfo.DefineID.TypeSpecificID = new_Skin.M762;
                     break;
                  }
                 }
                 for (int i = 0; i < sizeof(m7mag) / sizeof(m7mag[0]); i++) {
                 if (weaponid == m7mag[i]) {
                     weaponInfo.DefineID.TypeSpecificID = new_Skin.M762_Mag;
                     break;
                  }
                 }
                }
        ///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////     
        
    
                 if (weapowep == 101004){
                 for (int i = 0; i < sizeof(M4161) / sizeof(M4161[0]); i++) {
                 if (weaponid == M4161[i]) {
                     weaponInfo.DefineID.TypeSpecificID = new_Skin.M416_1;
                     break;
                  }
                 }
                 
                 for (int i = 0; i < sizeof(M4162) / sizeof(M4162[0]); i++) {
                 if (weaponid == M4162[i]) {
                     weaponInfo.DefineID.TypeSpecificID = new_Skin.M416_2;
                     break;
                  }
                 }
                 
                 for (int i = 0; i < sizeof(M4163) / sizeof(M4163[0]); i++) {
                 if (weaponid == M4163[i]) {
                     weaponInfo.DefineID.TypeSpecificID = new_Skin.M416_3;
                     break;
                  }
                  }
                 
                 for (int i = 0; i < sizeof(m4stock) / sizeof(m4stock[0]); i++) {
                 if (weaponid == m4stock[i]) {
                     weaponInfo.DefineID.TypeSpecificID = new_Skin.M416_4;
                     break;
                  }
                 }
                
                 for (int i = 0; i < sizeof(M416flash) / sizeof(M416flash[0]); i++) {
                 if (weaponid == M416flash[i]) {
                 weaponInfo.DefineID.TypeSpecificID = new_Skin.M416_flash;
                 break;
                 }
                 }
                 
                 for (int i = 0; i < sizeof(M416compe) / sizeof(M416compe[0]); i++) {
                 if (weaponid == M416compe[i]) {
                 weaponInfo.DefineID.TypeSpecificID = new_Skin.M416_compe;
                 break;
                 }
                 }
                 
                 for (int i = 0; i < sizeof(M416silent) / sizeof(M416silent[0]); i++) {
                 if (weaponid == M416silent[i]) {
                 weaponInfo.DefineID.TypeSpecificID = new_Skin.M416_silent;
                 break;
                 }
                 }
                 /*
                 for (int i = 0; i < sizeof(M416reddot) / sizeof(M416reddot[0]); i++) {
                 if (weaponid == M416reddot[i]) {
                 weaponInfo.DefineID.TypeSpecificID = new_Skin.M416_reddot;
                 break;
                 }
                 }
                 */
                 for (int i = 0; i < sizeof(M416holo) / sizeof(M416holo[0]); i++) {
                 if (weaponid == M416holo[i]) {
                 weaponInfo.DefineID.TypeSpecificID = new_Skin.M416_holo;
                 break;
                 }
                 }
                
                 for (int i = 0; i < sizeof(M416x2) / sizeof(M416x2[0]); i++) {
                 if (weaponid == M416x2[i]) {
                 weaponInfo.DefineID.TypeSpecificID = new_Skin.M416_x2;
                 break;
                 }
                 }
                 
                 for (int i = 0; i < sizeof(M416x3) / sizeof(M416x3[0]); i++) {
                 if (weaponid == M416x3[i]) {
                 weaponInfo.DefineID.TypeSpecificID = new_Skin.M416_x3;
                 break;
                 }
                 }
                 
                 for (int i = 0; i < sizeof(M416x4) / sizeof(M416x4[0]); i++) {
                 if (weaponid == M416x4[i]) {
                 weaponInfo.DefineID.TypeSpecificID = new_Skin.M416_x4;
                 break;
                 }
                 }
                 
                 for (int i = 0; i < sizeof(M416x6) / sizeof(M416x6[0]); i++) {
                 if (weaponid == M416x6[i]) {
                 weaponInfo.DefineID.TypeSpecificID = new_Skin.M416_x6;
                 break;
                 }
                 }
                 
                 for (int i = 0; i < sizeof(M416quickMag) / sizeof(M416quickMag[0]); i++) {
                 if (weaponid == M416quickMag[i]) {
                 weaponInfo.DefineID.TypeSpecificID = new_Skin.M416_quickMag;
                 break;
                 }
                 }
                 
                 for (int i = 0; i < sizeof(M416extendedMag) / sizeof(M416extendedMag[0]); i++) {
                 if (weaponid == M416extendedMag[i]) {
                 weaponInfo.DefineID.TypeSpecificID = new_Skin.M416_extendedMag;
                 break;
                 }
                 }
                 
                 for (int i = 0; i < sizeof(M416quickNextended) / sizeof(M416quickNextended[0]); i++) {
                 if (weaponid == M416quickNextended[i]) {
                 weaponInfo.DefineID.TypeSpecificID = new_Skin.M416_quickNextended;
                 break;
                 }
                 }
                 
                 for (int i = 0; i < sizeof(M416stock) / sizeof(M416stock[0]); i++) {
                 if (weaponid == M416stock[i]) {
                 weaponInfo.DefineID.TypeSpecificID = new_Skin.M416_stock;
                 break;
                 }
                 }
                 for (int i = 0; i < sizeof(M416angle) / sizeof(M416angle[0]); i++) {
                 if (weaponid == M416angle[i]) {
                 weaponInfo.DefineID.TypeSpecificID = new_Skin.M416_angle;
                 break;
                 }
                 }

                 for (int i = 0; i < sizeof(M416thumb) / sizeof(M416thumb[0]); i++) {
                 if (weaponid == M416thumb[i]) {
                 weaponInfo.DefineID.TypeSpecificID = new_Skin.M416_thumb;
                 break;
                 }
                 }
                 }
                 
  
                 
                 
                 
                 
                 
                 
                }
                 localPlayer->WeaponManagerComponent->CurrentWeaponReplicated->DelayHandleAvatarMeshChanged();
                 lastWeaponChangeTime = currentTime;
             }
         }
     }
 }
}}




if (Config.SkinOpen) {
  if (Config.CarMod){

if (localPlayer -> CurrentVehicle) {
        if (localPlayer -> CurrentVehicle -> VehicleAvatar) {
          std::string SkinIDStr = std::to_string((int) localPlayer -> CurrentVehicle -> VehicleAvatar -> GetDefaultAvatarID());
          Active::SkinCarDefault = localPlayer -> CurrentVehicle -> GetAvatarID();

          
          if (strstr(SkinIDStr.c_str(), "1901")) {
            Active::SkinCarMod = new_Skin.Moto;
            Active::SkinCarNew = true;
          } else if (strstr(SkinIDStr.c_str(), "1903")) {
            Active::SkinCarMod = new_Skin.Dacia;
            Active::SkinCarNew = true;
          } else if (strstr(SkinIDStr.c_str(), "1911")) {
            Active::SkinCarMod = new_Skin.Boat;
            Active::SkinCarNew = true;
          } else if (strstr(SkinIDStr.c_str(), "1904")) {
            Active::SkinCarMod = new_Skin.MiniBus;
            Active::SkinCarNew = true;
          } else if (strstr(SkinIDStr.c_str(), "1914")) {
            Active::SkinCarMod = new_Skin.Mirado;
            Active::SkinCarNew = true;
          } else if (strstr(SkinIDStr.c_str(), "1915")) {
            Active::SkinCarMod = new_Skin.Mirado;
            Active::SkinCarNew = true;
          } else if (strstr(SkinIDStr.c_str(), "1907")) {
            Active::SkinCarMod = new_Skin.Buggy;
            Active::SkinCarNew = true;
          }else if (strstr(SkinIDStr.c_str(), "1961")) {
            Active::SkinCarMod = new_Skin.CoupeRP;
            Active::SkinCarNew = true;
          } else if (strstr(SkinIDStr.c_str(), "1953")) {
            Active::SkinCarMod = new_Skin.Bigfoot;
            Active::SkinCarNew = true;
          } else if (strstr(SkinIDStr.c_str(), "1908")) {
            Active::SkinCarMod = new_Skin.UAZ;
            Active::SkinCarNew = true;
          } else Active::SkinCarNew = false;

          if (Active::SkinCarDefault != Active::SkinCarMod && Active::SkinCarNew) {
            
            localPlayer -> CurrentVehicle -> VehicleAvatar -> ChangeItemAvatar(Active::SkinCarMod, true);
          }
        }
      }
}
      }
    
      
      
      
    
               
                        auto PlayerCameraManager = localPlayerController->PlayerCameraManager;
        
                    
                    CameraCache =PlayerCameraManager->CameraCache;
        
                    int totalEnemies = 0, totalBots = 0;
                   /* 
                    
                if (Config.PlayerESP.Radar) {
                            bool out = false;
                            struct Vector3 Pos;
                            Pos.X = screenWidth / 5.5;
                            Pos.Y = screenHeight / 4.1;
                            struct Vector3 Size;
                            Size.X = 150;
                            Size.Y = 150;    
                            float RadarCenterX = Pos.X + (Size.X / 2);
                            float RadarCenterY = Pos.Y + (Size.Y / 2);
                           DrawLine(HUD, FVector2D(RadarCenterX, RadarCenterY), FVector2D(RadarCenterX, Pos.Y), 1, COLOR_WHITE);
                            DrawLine(HUD, FVector2D(RadarCenterX, RadarCenterY), FVector2D(Pos.X , RadarCenterY), 1, COLOR_WHITE);
                            DrawLine(HUD, FVector2D(Pos.X, RadarCenterY), FVector2D(Pos.X + Size.X, RadarCenterY), 1, COLOR_WHITE);
                            DrawLine(HUD, FVector2D(RadarCenterX, RadarCenterY), FVector2D(RadarCenterX, Pos.Y + Size.Y), 1, COLOR_WHITE); 
                            DrawLine(HUD, FVector2D{RadarCenterX + 74, RadarCenterY - 74}, FVector2D{RadarCenterX, RadarCenterY}, 1, COLOR_WHITE);
                            DrawLine(HUD, FVector2D{RadarCenterX - 74, RadarCenterY - 74}, FVector2D{RadarCenterX, RadarCenterY}, 1, COLOR_WHITE);
                            DrawLine(HUD, FVector2D{RadarCenterX, RadarCenterY}, FVector2D{RadarCenterX, RadarCenterY}, 1, COLOR_WHITE);
                   
                            DrawFilledCircle77(HUD, RadarCenterX, RadarCenterY, 40.f, COLOR_RED);
                         
                            DrawFilledCircle77(HUD, RadarCenterX + 0.5f, RadarCenterY + 0.5f, 40.f, COLOR_LIME);
                            DrawCircle(HUD, RadarCenterX, RadarCenterY, 102.f, 100.0f, COLOR_WHITE);
                          //  DrawRectangle(HUD, {RadarCenterX, RadarCenterY}, 150, 150, 1.0f, COLOR_WHITE);
                            
                         //   DrawCircle33(HUD, screenWidth / 2, screenHeight / 2, Config.AimBot.Cross, 1.0f, COLOR_THISTLE);

                            
                        }
                        
                        
                                    */
                                           if (Config.PlayerESP.Radar) {
                bool out = false;
                struct Vector3 Pos;
                Pos.X = screenWidth / 5.5;//4.395
                Pos.Y = screenHeight / 4.1;//40
                    
                struct Vector3 Size;
                Size.X = 130;
                Size.Y = 130;                   
                float RadarCenterX = Pos.X + (Size.X / 2);
                float RadarCenterY = Pos.Y + (Size.Y / 2);
                
                DrawFilledRectangle(HUD, {Pos.X, Pos.Y}, Size.X, Size.Y, {0, 0, 0, 0.23f});
                DrawLine(HUD, FVector2D{Pos.X, Pos.Y}, FVector2D{Pos.X + Size.X, Pos.Y}, 1, COLOR_WHITE);
                DrawLine(HUD, FVector2D{Pos.X, Pos.Y + Size.Y}, FVector2D{Pos.X + Size.X, Pos.Y + Size.Y}, 1, COLOR_WHITE);
                DrawLine(HUD, FVector2D{Pos.X, Pos.Y}, FVector2D{Pos.X, Pos.Y + Size.Y}, 1, COLOR_WHITE);
                DrawLine(HUD, FVector2D{Pos.X + Size.X, Pos.Y}, FVector2D{Pos.X + Size.X, Pos.Y + Size.Y}, 1, COLOR_WHITE);
                DrawLine(HUD, FVector2D{RadarCenterX, Pos.Y}, FVector2D{RadarCenterX, Pos.Y + Size.Y}, 1, COLOR_WHITE);
                DrawLine(HUD, FVector2D{Pos.X, RadarCenterY}, FVector2D{Pos.X + Size.X, RadarCenterY}, 1, COLOR_WHITE);
                DrawFilledCircleM(HUD, FVector2D{RadarCenterX, RadarCenterY}, 4.0, COLOR_WHITE);
                DrawRectangle(HUD, {Pos.X, Pos.Y}, Size.X, Size.Y, 1.0f, {1, 1, 1, 1.f});
             //   DrawFilledCircleM(HUD, FVector2D{RadarSketch.X,RadarSketch.Y}, 4.0, colV);
                
                
                
            }
                        
                        
                   std::vector<ASTExtraPlayerCharacter *> PlayerCharacter;
                    GetAllActors(PlayerCharacter);
     //int IntCount = 0;
                    for (auto actor = PlayerCharacter.begin(); actor != PlayerCharacter.end(); actor++)
                    {
                                auto Player = *actor;
                        if (Player->PlayerKey == localPlayer->PlayerKey)
                            continue;
                        if (Player->TeamID == localPlayer->TeamID)
                            continue;
                        if (Player->bDead)
                            continue;
                        if (Player->bHidden)
                            continue;
            if (!Player->Mesh)
continue;
if (Player->Health < 0.0f || Player->Health > 10000000.0f ) { continue; }

               
                        if (Config.Hidebot)
                        {
                            if (Player->bEnsure)
                                continue;
                        }
                    
                        if (Player->bEnsure)
                            totalBots++;
                        else
                            totalEnemies++;
                            
                          if (Player->IsInvincible)
                               IntCount ++;
                            
                            auto colV = FLinearColor();
                            
                    auto colV2 = FLinearColor();
                    auto colV4 = FLinearColor();
                    
                    
                    auto ColorHP = FLinearColor();
                    
                    auto ColorHP2 = FLinearColor();
                    
                    auto boxcolor = FLinearColor();
                    auto namecolor = FLinearColor();
                    
                    FVector HeadNrg5 = GetBoneLocationByName(Player, "Head");
                
                        if (!localPlayerController->LineOfSightTo(localPlayerController->PlayerCameraManager,HeadNrg5, true)) {    
                        colV = COLOR_RED;
                        
                        boxcolor = COLOR_RED;
                        colV4 = COLOR_RED2;
                    }else{
                        colV = COLOR_LIME;
                      boxcolor = COLOR_LIME;
                        colV4 = COLOR_LIME2;
                        
                    }
                            
                    
                         if (Player->bEnsure) {
                          //  totalBots++;
                            colV2 = COLOR_WHITE;
                            ColorHP = COLOR_LIME;
                            ColorHP2 = {0, 1.f, 0, 0.605f};
                            namecolor = COLOR_LIME;
                        }else{
                          //  totalEnemies++;
                          colV2 = COLOR_YELLOW;
                          ColorHP = COLOR_RED;
                          ColorHP2 = {1.f, 0, 0, 0.605f};
                    namecolor = {0.2f, 0.8f, 1.f, 1.f};
                    }
                    
                   
                      if (Player->IsInvincible)
                            {
                                //yallow
                                boxcolor = COLOR_YELLOW;
                                
                            }
                    
                                         bool IsVisible = localPlayerController->LineOfSightTo(localPlayerController->PlayerCameraManager,HeadNrg5, true);
                            if(IsVisible) {
                                if(Player->bEnsure){
                                    //white color
                                    visCol.R = 1.f;
                                    visCol.G = 1.f;
                                    visCol.B = 1.f;
                                    visCol.A = 1.f;
                                }else if(Player->Health == 0.0f){
                                    //drak green if knock down and visible
                                    visCol.R = 0.0f;
                                    visCol.G = 0.3f;
                                    visCol.B = 0.0f;
                                    visCol.A = 1.0f;
                                }else{
                                    //green
                                    visCol.R = 0.f;
                                    visCol.G = 1.f;
                                    visCol.B = 0.f;
                                    visCol.A = 1.f;
                                }
                            }else if (Player->bEnsure){
                                //gray R G B A
                                //    FLinearColor Gray = { 0.501960814f, 0.501960814f, 0.501960814f, 1.000000000f };
                                visCol.R = 0.435294117f;
                                visCol.G = 0.501960814f;
                                visCol.B = 0.501960814f;
                                visCol.A = 1.000000000f;
                                // if knocked down player color
                            }else if(Player->Health == 0.0f){
                                //drak++ red if knocked down and not visible
                                visCol.R = 0.549019607f;
                                visCol.G = 0.039215686f;
                                visCol.B = 0.050980392f;
                                visCol.A = 1.0f;
                            }else{
                                //red
                                visCol.R = 1.f;
                                visCol.G = 0.f;
                                visCol.B = 0.f;
                                visCol.A = 1.f;
                            }
                            if (Player->IsInvincible)
                            {
                                //yallow
                                visCol.R = 1.f;
                                visCol.G = 1.f;
                                visCol.B = 0.f;
                                visCol.A = 1.f;
                            }
                    
                               if (Config.Alert)
                            {
                              auto AboveHead = Player->GetHeadLocation(true);
                                    
                                    FVector2D AboveHeadSc;
         bool shit = false;
         FVector MyPosition, EnemyPosition;
                            ASTExtraVehicleBase * CurrentVehiclea = Player->CurrentVehicle;
                            if (CurrentVehiclea) {
                                MyPosition = CurrentVehiclea->RootComponent->RelativeLocation;
                            } else {
                                MyPosition = Player->RootComponent->RelativeLocation;
                            }
                            ASTExtraVehicleBase * CurrentVehicle = localPlayer->CurrentVehicle;
                            if (CurrentVehicle) {
                                EnemyPosition = CurrentVehicle->RootComponent->RelativeLocation;
                            } else {
                                EnemyPosition = localPlayer->RootComponent->RelativeLocation;
                            }
                            FVector EntityPos = WorldToRadar(localPlayerController->PlayerCameraManager->CameraCache.POV.Rotation.Yaw, MyPosition, EnemyPosition, NULL, NULL, Vector3(screenWidth, screenHeight, 0), shit);
                            FVector angle = FVector();
                            Vector3 forward = Vector3((float)(screenWidth / 2) - EntityPos.X, (float)(screenHeight / 2) - EntityPos.Y, 0.0f);
                        //    VectorAnglesRadar2(forward, angle);
                        VectorAnglesRadar(forward, angle);
                            const auto angle_yaw_rad = DEG2RAD(angle.Y + 180.f);//90
                            const auto new_point_x = (screenWidth / 2) + (70) / 2 * 8 * cosf(angle_yaw_rad);
                            const auto new_point_y = (screenHeight / 2) + (70) / 2 * 8 * sinf(angle_yaw_rad);
                           // std::array<Vector3, 3> points { Vector3(new_point_x - ((90) / 4 + 3.5f) / 2, new_point_y - ((55) / 4 + 3.5f) / 2, 0.f), Vector3(new_point_x + ((90) / 4 + 3.5f) / 4, new_point_y, 0.f), Vector3(new_point_x - ((90) / 4 + 3.5f) / 2, new_point_y + ((55) / 4 + 3.5f) / 2, 0.f)};
                      //      std::array<Vector3, 3> points { Vector3(new_point_x - ((110) / 4 + 3.5f) / 2, new_point_y - ((55) / 4 + 3.5f) / 2, 0.f), Vector3(new_point_x + ((110) / 4 + 3.5f) / 4, new_point_y, 0.f), Vector3(new_point_x - ((110) / 4 + 3.5f) / 2, new_point_y + ((55) / 4 + 3.5f) / 2, 0.f)};
                         //   std::array<Vector3, 3> points { Vector3(new_point_x - ((45) / 4 + 3.5f) / 2, new_point_y - ((55) / 4 + 3.5f) / 2, 0.f), Vector3(new_point_x + ((45) / 4 + 3.5f) / 4, new_point_y, 0.f), Vector3(new_point_x - ((45) / 4 + 3.5f) / 2, new_point_y + ((55) / 4 + 3.5f) / 2, 0.f)};
                            
                         std::array < FVector2D, 39 > points
                                {
                                    
                  FVector2D((float)new_point_x - (5.6f * (float)1.7f), new_point_y - (7.3f *1.7f)),
                  FVector2D((float)new_point_x + (11.6f *1.7f), new_point_y),
                  FVector2D((float)new_point_x - (5.6f *1.7f), new_point_y + (7.3f *1.7f)),
                  FVector2D((float)new_point_x - (5.6f *1.7f), new_point_y - (4.3f *1.7f)),
                  FVector2D((float)new_point_x - (19.5f *1.7f), new_point_y - (4.3f *1.7f)),
                  FVector2D((float)new_point_x - (19.5f *1.7f), new_point_y + (4.3f *1.7f)),
                  FVector2D((float)new_point_x - (5.6f *1.7f), new_point_y + (4.3f *1.7f)),
                  
                  FVector2D((float)new_point_x + (10.3f *1.7f), new_point_y),
                  FVector2D((float)new_point_x - (5.f *1.7f), new_point_y - (3.5f *1.7f)),
                  FVector2D((float)new_point_x - (5.f *1.7f), new_point_y + (3.5f *1.7f)),
                  
                  FVector2D((float)new_point_x - (5.f * (float)1.7f), new_point_y - (6.5f *1.7f)),
                  FVector2D((float)new_point_x - (5.f * (float)1.7f), new_point_y - (5.5f *1.7f)),
                  FVector2D((float)new_point_x - (5.f * (float)1.7f), new_point_y - (4.5f *1.7f)),
                  FVector2D((float)new_point_x - (5.f * (float)1.7f), new_point_y - (3.5f *1.7f)),
                  FVector2D((float)new_point_x - (5.f * (float)1.7f), new_point_y - (2.5f *1.7f)),
                  FVector2D((float)new_point_x - (5.f * (float)1.7f), new_point_y - (1.5f *1.7f)),
                  FVector2D((float)new_point_x - (5.f * (float)1.7f), new_point_y - (0.5f *1.7f)),
                  FVector2D((float)new_point_x - (5.f * (float)1.7f), new_point_y + (6.5f *1.7f)),
                  FVector2D((float)new_point_x - (5.f * (float)1.7f), new_point_y + (5.5f *1.7f)),
                  FVector2D((float)new_point_x - (5.f * (float)1.7f), new_point_y + (4.5f *1.7f)),

                     FVector2D((float)new_point_x - (5.f * (float)1.7f), new_point_y + (3.5f *1.7f)),
                                   FVector2D((float)new_point_x - (5.f * (float)1.7f), new_point_y + (2.5f *1.7f)),
                                   FVector2D((float)new_point_x - (5.f * (float)1.7f), new_point_y + (1.5f *1.7f)),
                                   FVector2D((float)new_point_x - (5.f * (float)1.7f), new_point_y),
                                   FVector2D((float)new_point_x - (5.f * (float)1.7f), new_point_y + (0.2f *1.7f)),
                                   FVector2D((float)new_point_x - (5.f * (float)1.7f), new_point_y - (0.2f *1.7f)),
                                   
                                   FVector2D((float)new_point_x - (18.5f *1.7f), new_point_y - (3.75f *1.7f)),
                                   FVector2D((float)new_point_x - (18.5f *1.7f), new_point_y - (3.f *1.7f)),
                                   FVector2D((float)new_point_x - (18.5f *1.7f), new_point_y - (1.7f *1.7f)),
                                   FVector2D((float)new_point_x - (18.5f *1.7f), new_point_y - (1.f *1.7f)),
                                   FVector2D((float)new_point_x - (18.5f *1.7f), new_point_y - (0.5f *1.7f)),
                                   FVector2D((float)new_point_x - (18.5f *1.7f), new_point_y - (0.2f *1.7f)),
                                   FVector2D((float)new_point_x - (18.5f *1.7f), new_point_y),
                                   FVector2D((float)new_point_x - (18.5f *1.7f), new_point_y + (3.75f *1.7f)),
                                   FVector2D((float)new_point_x - (18.5f *1.7f), new_point_y + (3.f *1.7f)),
                                   FVector2D((float)new_point_x - (18.5f *1.7f), new_point_y + (1.7f *1.7f)),
                                   FVector2D((float)new_point_x - (18.5f *1.7f), new_point_y + (1.f *1.7f)),
                                   FVector2D((float)new_point_x - (18.5f *1.7f), new_point_y + (0.5f *1.7f)),
                                   FVector2D((float)new_point_x - (18.5f *1.7f), new_point_y + (0.2f *1.7f)),
                     
                                };

                         
                     //       RotateTriangle2(points, angle.Y + 180.f);
                                 
                                  if (gGameplayStatics->ProjectWorldToScreen(localPlayerController, AboveHead, false,  &AboveHeadSc)) {
                                    
                             // 
                             
                            }else{
                                
                                
                                   
                                    
             
                                    
                                    RotateTriangle55(points, angle.Y + 180.f);
                                    float Thickness = 1.7f;
                                  
                          
                                    DrawArrows(HUD,points, Thickness, colV4);
                                
                                
                                
                                
                                
                                
                    //       DrawArrow(HUD, {points.at(0).X,points.at(0).Y},{points.at(1).X,points.at(1).Y},{points.at(2).X,points.at(2).Y}, 3.f, colV);
                            }
                     }
                            
                           /*
                    //绘制雷达
                        if (Config.PlayerESP.Radar) {
                            bool out = false;
                            struct Vector3 Pos;
                            Pos.X = screenWidth / 5.5;
                            Pos.Y = screenHeight / 4.1;
                            struct Vector3 Size;
                            Size.X = 150;
                            Size.Y = 150;    
                            float RadarCenterX = Pos.X + (Size.X / 2);
                            float RadarCenterY = Pos.Y + (Size.Y / 2);
                            FVector MyPosition, EnemyPosition;
                            ASTExtraVehicleBase * CurrentVehiclea = Player->CurrentVehicle;
                            
                            if (CurrentVehiclea) {
                                MyPosition = CurrentVehiclea->RootComponent->RelativeLocation;
                            } else {
                                MyPosition = Player->RootComponent->RelativeLocation;
                            }
                            
                            ASTExtraVehicleBase * CurrentVehicle = localPlayer->CurrentVehicle;
                            if (CurrentVehicle) {
                                EnemyPosition = CurrentVehicle->RootComponent->RelativeLocation;
                            } else {
                                EnemyPosition = localPlayer->RootComponent->RelativeLocation;
                            }
                            FVector RadarSketch = WorldToRadar(localPlayerController->PlayerCameraManager->CameraCache.POV.Rotation.Yaw, MyPosition, EnemyPosition, Pos.X, Pos.Y, Vector3(Size.X, Size.Y, 0), out);
               
                            //绘制雷达内部线条
                            DrawFilledCircleM(HUD, FVector2D{RadarSketch.X,RadarSketch.Y}, 4.0, colV);
                        }
                            
                        
                        */
                           //      float textsize=screenHeight/50;
            if (Config.PlayerESP.Radar) {
                bool out = false;
                struct Vector3 Pos;
                Pos.X = screenWidth / 5.5;//4.395
                Pos.Y = screenHeight / 4.1;//40
                    
                struct Vector3 Size;
                Size.X = 130;
                Size.Y = 130;                   
                float RadarCenterX = Pos.X + (Size.X / 2);
                float RadarCenterY = Pos.Y + (Size.Y / 2);
                FVector MyPosition, EnemyPosition;
                ASTExtraVehicleBase * CurrentVehiclea = Player->CurrentVehicle;
                if (CurrentVehiclea) {
                    MyPosition = CurrentVehiclea->RootComponent->RelativeLocation;
                } else {
                    MyPosition = Player->RootComponent->RelativeLocation;
                }
                ASTExtraVehicleBase * CurrentVehicle = localPlayer->CurrentVehicle;
                if (CurrentVehicle) {
                    EnemyPosition = CurrentVehicle->RootComponent->RelativeLocation;
                } else {
                    EnemyPosition = localPlayer->RootComponent->RelativeLocation;
                }
                FVector RadarSketch = WorldToRadar(localPlayerController->PlayerCameraManager->CameraCache.POV.Rotation.Yaw, MyPosition, EnemyPosition, Pos.X, Pos.Y, Vector3(Size.X, Size.Y, 0), out);
             //   m_Canvas->drawBorder(RadarSketch.X, RadarSketch.Y, 2, 2, 7.4f, SCOLOR2);
          //      m_Canvas->drawBorder(Pos.X, Pos.Y, 200, 200, 1.4f, WHITE);
                //m_Canvas->drawCircle(RadarCenterX + 0.5f, RadarCenterY + 0.5f, 2, 5, false, RED);
                /*
                DrawFilledRectangle(HUD, {Pos.X, Pos.Y}, Size.X, Size.Y, {0, 0, 0, 0.23f});
                DrawLine(HUD, FVector2D{Pos.X, Pos.Y}, FVector2D{Pos.X + Size.X, Pos.Y}, 1, COLOR_WHITE);
                DrawLine(HUD, FVector2D{Pos.X, Pos.Y + Size.Y}, FVector2D{Pos.X + Size.X, Pos.Y + Size.Y}, 1, COLOR_WHITE);
                DrawLine(HUD, FVector2D{Pos.X, Pos.Y}, FVector2D{Pos.X, Pos.Y + Size.Y}, 1, COLOR_WHITE);
                DrawLine(HUD, FVector2D{Pos.X + Size.X, Pos.Y}, FVector2D{Pos.X + Size.X, Pos.Y + Size.Y}, 1, COLOR_WHITE);
                DrawLine(HUD, FVector2D{RadarCenterX, Pos.Y}, FVector2D{RadarCenterX, Pos.Y + Size.Y}, 1, COLOR_WHITE);
                DrawLine(HUD, FVector2D{Pos.X, RadarCenterY}, FVector2D{Pos.X + Size.X, RadarCenterY}, 1, COLOR_WHITE);
                DrawFilledCircleM(HUD, FVector2D{RadarCenterX, RadarCenterY}, 4.0, COLOR_WHITE);
                DrawRectangle(HUD, {Pos.X, Pos.Y}, Size.X, Size.Y, 1.0f, {1, 1, 1, 1.f});
                */
                DrawFilledCircleM(HUD, FVector2D{RadarSketch.X,RadarSketch.Y}, 4.0, colV);
                
                
                
            }
                        
                        
            
            
            
         
            
           
            
                        
                        
                          float Distance = Player->GetDistanceTo(localPlayer) / 100.f;
                        if (Distance < 1000.f)
                        {
                            /*
                                        float magic_number = (Distance);
float mx = (screenWidth / 4) / magic_number;
float healthLength = screenWidth / 17;
if (healthLength < mx)
healthLength = mx;

                        FVector HeadPos2 = GetBoneLocationByName(Player, "Head");
                        HeadPos2.Z += 17.5f;//16.5f;
                        FVector RootPos2 = GetBoneLocationByName(Player, "Root");
                        RootPos2.Z -= 6.5f;//5.5f;
                    //targetAimPos.Z -= 25.f;
                      auto Head_RootZ = HeadPos2.Z - RootPos2.Z;
                        */
                            FVector HeadPos2 = GetBoneLocationByName(Player, "Head");
                        HeadPos2.Z += 17.5f;//16.5f;
                        FVector RootPos2 = GetBoneLocationByName(Player, "Root");
                        RootPos2.Z -= 6.5f;//5.5f;
                    //targetAimPos.Z -= 25.f;
                      auto Head_RootZ = HeadPos2.Z - RootPos2.Z;
                            
                      
                        FVector Head = GetBoneLocationByName(Player, "Head");
                   //     Head.Z += 12.5f;
                        FVector Root = GetBoneLocationByName(Player, "Root");
                        FVector Head66 = GetBoneLocationByName(Player, "Head");
                        /*
                              float magic_number = (Distance);
                    float mx = (SizeX / 4) / magic_number;

                    float healthLength = SizeX / 19;
                    if (healthLength < mx)
                        healthLength = mx;
*/
                        FVector uparmr = GetBoneLocationByName(Player, "upperarm_r");
                        FVector uparml = GetBoneLocationByName(Player, "upperarm_l");
                        FVector lowarmr = GetBoneLocationByName(Player, "lowerarm_r");
                        FVector lowarml = GetBoneLocationByName(Player, "lowerarm_l");
                        FVector handr = GetBoneLocationByName(Player, "hand_r");
                        FVector handl = GetBoneLocationByName(Player, "hand_l");
                        FVector itemr = GetBoneLocationByName(Player, "item_r");
                        FVector iteml = GetBoneLocationByName(Player, "item_l");
                        
                        
                        
                        FVector clavicler = GetBoneLocationByName(Player, "clavicle_r");
                        FVector claviclel = GetBoneLocationByName(Player, "clavicle_l");
                        
                        FVector neck = GetBoneLocationByName(Player, "neck_01");
                        FVector spain01 = GetBoneLocationByName(Player, "spine_01");
                        FVector spain02 = GetBoneLocationByName(Player, "spine_02");
                        FVector spain03 = GetBoneLocationByName(Player, "spine_03");
                        FVector pelvis = GetBoneLocationByName(Player, "pelvis");
                        
                        FVector calfl = GetBoneLocationByName(Player, "calf_l");
                        FVector calfr = GetBoneLocationByName(Player, "calf_r");
                        FVector thighl = GetBoneLocationByName(Player, "thigh_l");
                        FVector thighr = GetBoneLocationByName(Player, "thigh_r");
                        
                        
                        
                        FVector footr = GetBoneLocationByName(Player, "foot_r");
                        FVector footl = GetBoneLocationByName(Player, "foot_l");
                        
                        FVector2D uparmrSC, uparmlSC, lowarmrSC, lowarmlSC, handrSC, handlSC, itemrSC, itemlSC, upperarmtwist01rSC, upperarmtwist01lSC, claviclerSC, claviclelSC, neckSC, spain01SC, spain02SC, spain03SC, pelvisSC;
                        FVector2D calflSC,calfrSC,thighlSC,thighrSC,calftwist01lSC,calftwist01rSC,thightwist01lSC,thightwist01rSC,footrSC,footlSC,lowerarmtwist01lSC,lowerarmtwist01rSC;
                        
                        
                        
                        
                       
                        
                //     auto HeadNrg = Player->GetBonePos("Head", {});
                
                
                     FVector HeadNrg = GetBoneLocationByName(Player, "Head");
                     HeadNrg.Z += 35.f;
                    FVector2D HeadNrgSc;
                    HeadNrgSc =  WorldToScreen(HeadNrg, CameraCache.POV, screenWidth, screenHeight);
             
                        
                        
                      
                        
                    
                            FVector2D HeadSc, RootSc;
                            
                                        if (Config.EspLine) {
                               if (LINE360N180 == 0) {
                                        auto mWidthScale = std::min(0.006f * Distance, 100.f);
                                    auto mWidth = 20.f - mWidthScale;
                                    auto mHeight = mWidth * 0.125f;
                                    
                                    HeadNrgSc.Y -= (mHeight * 1.4f + 15);
                            
                     Canvas->K2_DrawLine({(float)screenWidth/2 , 10}, {HeadNrgSc.X , HeadNrgSc.Y - 25.5}, 1, colV);//25.5 / 20
          }
                            
                       }
                     
                            
                            //if (gGameplayStatics->ProjectWorldToScreen(localPlayerController, Head, false, &HeadSc) && gGameplayStatics->ProjectWorldToScreen(localPlayerController, Root, false, &RootSc))
                          if(W2S(Head, (FVector2D *) & HeadSc) && W2S(Root, (FVector2D *) & RootSc) &&W2S(uparmr, (FVector2D *) & uparmrSC) && W2S(uparml, (FVector2D *) & uparmlSC) &&W2S(lowarml, (FVector2D *) & lowarmlSC) &&W2S(lowarmr, (FVector2D *) & lowarmrSC) &&W2S(handr, (FVector2D *) & handrSC)&&W2S(handl, (FVector2D *) & handlSC) &&W2S(itemr, (FVector2D *) & itemrSC)&&W2S(iteml, (FVector2D *) & itemlSC)&&W2S(clavicler, (FVector2D *) & claviclerSC)&&W2S(claviclel, (FVector2D *) & claviclelSC) &&W2S(neck, (FVector2D *) & neckSC) &&W2S(spain01, (FVector2D *) & spain01SC) &&W2S(spain02, (FVector2D *) & spain02SC) &&W2S(spain03, (FVector2D *) & spain03SC) &&W2S(pelvis, (FVector2D *) & pelvisSC) &&W2S(calfl, (FVector2D *) & calflSC)&&W2S(calfr, (FVector2D *) & calfrSC) &&W2S(thighl, (FVector2D *) & thighlSC)&&W2S(thighr, (FVector2D *) & thighrSC)&&W2S(footr, (FVector2D *) & footrSC)&&W2S(footl, (FVector2D *) & footlSC)) {
                                
          
                              
                                        if (Config.EspLine) {
                               if (LINE360N180 == 1) {
                                        auto mWidthScale = std::min(0.006f * Distance, 100.f);
                                    auto mWidth = 20.f - mWidthScale;
                                    auto mHeight = mWidth * 0.125f;
                                    
                                    HeadNrgSc.Y -= (mHeight * 1.4f + 15);
                            
                     Canvas->K2_DrawLine({(float)screenWidth/2 , 10}, {HeadNrgSc.X , HeadNrgSc.Y - 25.5}, 1, colV);//25.5 / 20
          }
                            
                       }
                     
                              
                              /*
                              
                           //     if(!offscreen(HeadSc, {(float)SizeX , (float)SizeY}) ){
                              float boxHeight = abs(HeadSc.Y);
                              float boxWidth = boxHeight * 0.65f;
                        
                                
                                          if (Config.PlayerESP.Line) {
                               if (Config.EspLinetow == 1) {
                                      //  auto mWidthScale = std::min(0.006f * Distance, 100.f);
                                  //  auto mWidth = 20.f - mWidthScale;
                                  //  auto mHeight = mWidth * 0.125f;
                                    
                                 //   HeadSc.Y -= (mHeight * 1.4f + 15);
                            
              //       Canvas->K2_DrawLine({(float)SizeX/2 , 20}, {HeadSc.X , HeadSc.Y - 25.0}, 1, colV);//25.5
                     
               //      DrawLine(HUD, {(float)SizeX/2 , 20}, {HeadSc.X , HeadSc.Y - 25.0}, 1, colV);
                     
                     
                     
                               auto mWidthScale = std::min(0.006f * Distance, 100.f);
                                    auto mWidth = 20.f - mWidthScale;
                                    auto mHeight = mWidth * 0.125f;
                                    
                                    HeadNrgSc.Y -= (mHeight * 1.4f + 15);
                            
                     Canvas->K2_DrawLine({(float)screenWidth/2 , 20}, {HeadNrgSc.X , HeadNrgSc.Y - 25.0}, 1, colV);//25.5
         
          }
                            
                            }*/
                            
                                    
                     
                         
                        
                            
                            if (Config.Box)
                                {
                                     if (BOXUI1OR2 == 0) {
                                float He_RoZ = (float) Head_RootZ;
                                     
                                     FVector BoxSize;
                                     BoxSize.X = 120.f; //65
                                     BoxSize.Y = 130.f; //80
                                     BoxSize.Z = He_RoZ + 16; //165 8
                       Box3D(HUD, Player->K2_GetActorLocation(), BoxSize, boxcolor, 1.f);        
                 }
                       }
                   
                    if (Config.Box)
                                {
                                     if (BOXUI1OR2 == 1) {
                   float boxHeight = abs(HeadSc.Y - RootSc.Y);
                                    float boxWidth = boxHeight * 0.65f;
                                  //  Box4Line(draw, 0.5f, headPosSC.x - (boxWidth / 2), headPosSC.y, boxWidth, boxHeight,  PlayerBoxClrCf);
                                    Box4Line(HUD, HeadSc.X - (boxWidth / 2), HeadSc.Y, boxWidth, boxHeight, COLOR_WHITE);
                            }
                                    }
                                    
                                    
                                 if (Config.AimBot.Enable) {
                 //        DrawCircle33(HUD, pelvisSC.X, pelvisSC.Y, Config.AimBot.Cross, 1.0f, COLOR_THISTLE);
                            
                if (Config.AimTargetLine)
                {
                ASTExtraPlayerCharacter *Target = GetTargetForAimBot();
                            
                    if (Target)
                    {
                     FVector TargetPOS = GetBoneLocationByName(Target, "Head");//pelvis
                     TargetPOS.Z -= 10;
                     FVector2D TargetPOSSC;
                     if(W2S(TargetPOS, (FVector2D *) & TargetPOSSC)){
                     DrawLine(HUD, {static_cast<float>(screenWidth / 2), static_cast<float>(screenHeight / 2)}, TargetPOSSC, 1, COLOR_LIME);             
                     }
                    }
                    }
                }            
                          
                            
                            
                                if (Config.EspBone)
                                {
                                    
                                    
                                    
                                      if (!localPlayerController->LineOfSightTo(localPlayerController->PlayerCameraManager,neck, true))
                                {
                                    
                               
                    
                                DrawLine(HUD, HeadSc, neckSC, 1, COLOR_RED);
                                
                                
                                }else{
                                DrawLine(HUD, HeadSc, neckSC, 1, COLOR_LIME);
                                }
                                
                                if (!localPlayerController->LineOfSightTo(localPlayerController->PlayerCameraManager,clavicler, true))
                                {
                                DrawLine(HUD, neckSC, claviclerSC, 1, COLOR_RED);
                                }else{
                                DrawLine(HUD, neckSC, claviclerSC, 1, COLOR_LIME);
                                }
                                
                                if (!localPlayerController->LineOfSightTo(localPlayerController->PlayerCameraManager,uparmr, true))
                                {
                                DrawLine(HUD, claviclerSC, uparmrSC, 1, COLOR_RED);
                                }else{
                                DrawLine(HUD, claviclerSC, uparmrSC, 1, COLOR_LIME);
                                }
                                
                                if (!localPlayerController->LineOfSightTo(localPlayerController->PlayerCameraManager,lowarmr, true))
                                {
                                DrawLine(HUD, uparmrSC, lowarmrSC, 1, COLOR_RED);
                                }else{
                                DrawLine(HUD, uparmrSC, lowarmrSC, 1, COLOR_LIME);
                                }
                                
                                if (!localPlayerController->LineOfSightTo(localPlayerController->PlayerCameraManager,handr, true))
                                {
                                DrawLine(HUD, lowarmrSC, handrSC, 1, COLOR_RED);
                                }else{
                                DrawLine(HUD, lowarmrSC, handrSC, 1, COLOR_LIME);
                                }
                                
                                if (!localPlayerController->LineOfSightTo(localPlayerController->PlayerCameraManager,itemr, true))
                                {
                                DrawLine(HUD, handrSC, itemrSC, 1, COLOR_RED);
                                }else{
                                DrawLine(HUD, handrSC, itemrSC, 1, COLOR_LIME);
                                }                                                                                               
                                                                
                                
                                if (!localPlayerController->LineOfSightTo(localPlayerController->PlayerCameraManager,claviclel, true))
                                {
                                DrawLine(HUD, neckSC, claviclelSC, 1, COLOR_RED);
                                }else{
                                DrawLine(HUD, neckSC, claviclelSC, 1, COLOR_LIME);
                                }
                                
                                if (!localPlayerController->LineOfSightTo(localPlayerController->PlayerCameraManager,uparml, true))
                                {
                                DrawLine(HUD, claviclelSC, uparmlSC, 1, COLOR_RED);
                                }else{
                                DrawLine(HUD, claviclelSC, uparmlSC, 1, COLOR_LIME);
                                }
                                
                                if (!localPlayerController->LineOfSightTo(localPlayerController->PlayerCameraManager,lowarml, true))
                                {
                                DrawLine(HUD, uparmlSC, lowarmlSC, 1, COLOR_RED);
                                }else{
                                DrawLine(HUD, uparmlSC, lowarmlSC, 1, COLOR_LIME);
                                }
                                
                                if (!localPlayerController->LineOfSightTo(localPlayerController->PlayerCameraManager,handl, true))
                                {
                                DrawLine(HUD, lowarmlSC, handlSC, 1, COLOR_RED);
                                }else{
                                DrawLine(HUD, lowarmlSC, handlSC, 1, COLOR_LIME);
                                }
                                
                                if (!localPlayerController->LineOfSightTo(localPlayerController->PlayerCameraManager,iteml, true))
                                {
                                DrawLine(HUD, handlSC, itemlSC, 1, COLOR_RED);
                                }else{
                                DrawLine(HUD, handlSC, itemlSC, 1, COLOR_LIME);
                                }
                                
                                if (!localPlayerController->LineOfSightTo(localPlayerController->PlayerCameraManager,spain03, true))
                                {
                                DrawLine(HUD, neckSC, spain03SC, 1, COLOR_RED);
                                }else{
                                DrawLine(HUD, neckSC, spain03SC, 1, COLOR_LIME);
                                }
                                
                                if (!localPlayerController->LineOfSightTo(localPlayerController->PlayerCameraManager,spain02, true))
                                {
                                DrawLine(HUD, spain03SC, spain02SC, 1, COLOR_RED);
                                }else{
                                DrawLine(HUD, spain03SC, spain02SC, 1, COLOR_LIME);
                                }
                                
                                if (!localPlayerController->LineOfSightTo(localPlayerController->PlayerCameraManager,spain01, true))
                                {
                                DrawLine(HUD, spain02SC, spain01SC, 1, COLOR_RED);
                                }else{
                                DrawLine(HUD, spain02SC, spain01SC, 1, COLOR_LIME);
                                }
                                
                                if (!localPlayerController->LineOfSightTo(localPlayerController->PlayerCameraManager,pelvis, true))
                                {
                                DrawLine(HUD, spain01SC, pelvisSC, 1, COLOR_RED);
                                }else{
                                DrawLine(HUD, spain01SC, pelvisSC, 1, COLOR_LIME);
                                }
                                
                                if (!localPlayerController->LineOfSightTo(localPlayerController->PlayerCameraManager,thighr, true))
                                {
                                DrawLine(HUD, pelvisSC, thighrSC, 1, COLOR_RED);
                                }else{
                                DrawLine(HUD, pelvisSC, thighrSC, 1, COLOR_LIME);
                                }
                                
                                if (!localPlayerController->LineOfSightTo(localPlayerController->PlayerCameraManager,calfr, true))
                                {
                                DrawLine(HUD, thighrSC, calfrSC, 1, COLOR_RED);
                                }else{
                                DrawLine(HUD, thighrSC, calfrSC, 1, COLOR_LIME);
                                }
                                if (!localPlayerController->LineOfSightTo(localPlayerController->PlayerCameraManager,footr, true))
                                {
                                DrawLine(HUD, calfrSC, footrSC, 1, COLOR_RED);
                                }else{
                                DrawLine(HUD, calfrSC, footrSC, 1, COLOR_LIME);
                                }
                                
                                if (!localPlayerController->LineOfSightTo(localPlayerController->PlayerCameraManager,thighl, true))
                                {
                                DrawLine(HUD, pelvisSC, thighlSC, 1, COLOR_RED);
                                }else{
                                DrawLine(HUD, pelvisSC, thighlSC, 1, COLOR_LIME);
                                }
                                
                                if (!localPlayerController->LineOfSightTo(localPlayerController->PlayerCameraManager,calfl, true))
                                {
                                DrawLine(HUD, thighlSC, calflSC, 1, COLOR_RED);
                                }else{
                                DrawLine(HUD, thighlSC, calflSC, 1, COLOR_LIME);
                                }
                                if (!localPlayerController->LineOfSightTo(localPlayerController->PlayerCameraManager,footl, true))
                                {
                                DrawLine(HUD, calflSC, footlSC, 1, COLOR_RED);
                                }else{
                                DrawLine(HUD, calflSC, footlSC, 1, COLOR_LIME);
                                }
                                
                             
                                
                                }

                                
                                /*
                if (Config.PlayerESP.EspInfo)
                {
                                   float CurHP = std::max(0.f, std::min(Player->Health, Player->HealthMax));
                                    float MaxHP = Player->HealthMax;
                            
                                    if (CurHP == 0 && !Player->bDead)
                                    {
                                    ColorHP = {1.f, 1.f, 0, 1.f};
                                        CurHP = Player->NearDeathBreath;
                                        USTCharacterNearDeathComp *NearDeatchComponent = Player->NearDeatchComponent;
                                        if (NearDeatchComponent)
                                        {
                                            MaxHP = NearDeatchComponent->BreathMax;
                                        }
                                }
                                
                                auto AboveHead = Player->GetHeadLocation(true);
                                    AboveHead.Z += 35.f;
                                    FVector2D AboveHeadSc;
                                    if (gGameplayStatics->ProjectWorldToScreen(localPlayerController, AboveHead, false, &AboveHeadSc))
                                    {
                                    
                                        
                                    
                                    
                             //     auto mWidthScale = std::min(0.0f * Distance, 20.f);//27 
                                // i use  
                               auto mWidthScale = std::min(0.0f * 24.0f, 0.0f); //100  75 50 | 24
                                //    auto mWidthScale = std::min(0.10f * Distance, 50.f);

                                    auto mWidth2 = 10.0f - mWidthScale;
                                    auto mWidth3 = 20.0f - mWidthScale;
                                    auto mWidth4 = 30.0f - mWidthScale;
                                    auto mWidth5 = 40.0f - mWidthScale;
                                    auto mWidth = 70.0f - mWidthScale;
                                    auto mWidth99 = 5.0f - mWidthScale;
                                    
                                    
                                 //   float boxHeight = abs(HeadSc.Y - RootSc.Y);
                                    auto mHeight = mWidth * 0.050f;//098f //0.07f 050f
                                    
                                    auto mHeight2 = mWidth * -0.219f; //180f
                                    
                                    AboveHeadSc.X -= (mWidth / 2);
                                  AboveHeadSc.Y -= (mHeight * 1.5f);
                                  
                                  
                                   
                                DrawFilledRectangle(HUD, {AboveHeadSc.X, AboveHeadSc.Y - 7}, mWidth, mHeight2, GetRandomColorByIndexHud(Player->TeamID));
                                DrawFilledRectangle(HUD, {AboveHeadSc.X, AboveHeadSc.Y - 7}, mWidth, mHeight, COLOR_WHITE);
                                    DrawFilledRectangle(HUD, {AboveHeadSc.X, AboveHeadSc.Y - 7}, (CurHP * mWidth / MaxHP), mHeight, ColorHP);
//DrawFilledRectangle(HUD, {AboveHeadSc.X, AboveHeadSc.Y - 7}, (CurHP * mWidth / MaxHP), mHeight, ColorHP);
                
                                  
                                    DrawRectangle(HUD, {AboveHeadSc.X, AboveHeadSc.Y - 7}, mWidth, mHeight, 1.0f, {0, 0, 0, 1.f});
                                    
                                   
                                 }
                            }
                                         */
                            
                                         
                                         
                                         
                                         
                                         
                            /*
                if (Config.PlayerESP.EspInfo)
                {
                                   float CurHP = std::max(0.f, std::min(Player->Health, Player->HealthMax));
                                    float MaxHP = Player->HealthMax;
                            
                                    if (CurHP == 0 && !Player->bDead)
                                    {
                                    ColorHP = {1.f, 1.f, 0, 1.f};
                                        CurHP = Player->NearDeathBreath;
                                        USTCharacterNearDeathComp *NearDeatchComponent = Player->NearDeatchComponent;
                                        if (NearDeatchComponent)
                                        {
                                            MaxHP = NearDeatchComponent->BreathMax;
                                        }
                                }
                                
                                auto AboveHead = Player->GetHeadLocation(true);
                                    AboveHead.Z += 35.f;
                                    FVector2D AboveHeadSc;
                                    if (gGameplayStatics->ProjectWorldToScreen(localPlayerController, AboveHead, false, &AboveHeadSc))
                                    {
                                    float mWidth = 110.0f;
                                    float mHeight = 3.5f;
                                    float mHeight2 = -14.5f;
                                    float mWidth2 = 32.0f;
                                    AboveHeadSc.X -= (mWidth / 2);
                                  AboveHeadSc.Y -= (mHeight * 1.5f);
                                  
                                  
                                
                           //     DrawFilledRectangle(HUD, HeadScreenPos, BarWidth, BarHeight, HPColor);
                                   
                           
                           
                                DrawFilledRectangle(HUD, {AboveHeadSc.X, AboveHeadSc.Y - 7}, mWidth, mHeight2, GetRandomColorByIndexHud(Player->TeamID));
                                DrawFilledRectangle(HUD, {AboveHeadSc.X, AboveHeadSc.Y - 7}, mWidth2, mHeight2, GetRandomColorByIndexHud2(Player->TeamID));
                           
                                DrawFilledRectangle(HUD, {AboveHeadSc.X, AboveHeadSc.Y - 7}, mWidth, mHeight, COLOR_WHITE);
                                    DrawFilledRectangle(HUD, {AboveHeadSc.X, AboveHeadSc.Y - 7}, (CurHP * mWidth / MaxHP), mHeight, ColorHP);

                                    DrawRectangle(HUD, {AboveHeadSc.X, AboveHeadSc.Y - 7}, mWidth, mHeight, 1.0f, {0, 0, 0, 1.f});
                                    
                                    
                                 }
                            }
                                         
                            */
                               ////UI 3
                            
                     if (Config.Health)
                {
                      if (ESPUI1OR2 == 2) {
                                   float CurHP = std::max(0.f, std::min(Player->Health, Player->HealthMax));
                                    float MaxHP = Player->HealthMax;
                            FLinearColor ColorHP22 = {std::min(((510.f * (MaxHP - CurHP)) / MaxHP) / 255.f, 1.f), std::min(((510.f * CurHP) / MaxHP) / 255.f, 1.f), 0.f, 0.9f};
                                    if (CurHP == 0 && !Player->bDead)
                                    {
                                  //  ColorHP = {1.f, 1.f, 0, 1.f};
                                    ColorHP22 = {1.f, 0, 0, 0.9f};
                                        CurHP = Player->NearDeathBreath;
                                        USTCharacterNearDeathComp *NearDeatchComponent = Player->NearDeatchComponent;
                                        if (NearDeatchComponent)
                                        {
                                            MaxHP = NearDeatchComponent->BreathMax;
                                        }
                                }
                            
                                    if (gGameplayStatics->ProjectWorldToScreen(localPlayerController, Head, false, &HeadSc))
                                    {
                                    
float boxHeight = abs(HeadSc.Y -RootSc.Y);

                                    float boxWidth = 3.0f;
     DrawFilledRectangle(HUD, {RootSc.X +15.0f + (boxWidth / 2), RootSc.Y}, boxWidth, (CurHP * boxHeight / -MaxHP)/* - TEST555*/, ColorHP22);

                                    }
                                    
                                 }
                            }
                            
                            
                           if (Config.Name)
                                {
                                     if (ESPUI1OR2 == 2) {
                              if (gGameplayStatics->ProjectWorldToScreen(localPlayerController, Head, false, &HeadSc))
                                    {
                                    
                                    std::wstring ws;
                                    std::wstring wsBot;
                                   
                              if (Config.Name)
                                {
//ws = std::to_wstring(Player->TeamID);
                                            auto playername = Player->PlayerName;
                                            if (Player->bEnsure) {
                                            wsBot += E(L"Bot");
                                            } else {
                                          
                                                wsBot += playername.ToWString();
                                                
                                                }
                                              //  TopNrg = 30.0f;
                                        }
                                        
                                 
                                    auto mWidthScale = std::min(0.006f * Distance, 100.f);
                                    auto mWidth = 20.f - mWidthScale;
                                    auto mHeight = mWidth * 0.125f;
                                //    AboveHeadSc.X -= (mWidth / 2 - 20);
                                 //   AboveHeadSc.Y -= (mHeight * 1.4f + 15);
                                        
                                        float Distance2 = Player->GetDistanceTo(localPlayer) / 100.f;
                                    
                                 tslFont->LegacyFontSize = fmax(6, 10.0f - (Distance2 / 100));//4.5f, 10.5f //10.5
                                 
                             DrawOutlinedText(HUD, FString(wsBot), {HeadSc.X, HeadSc.Y  - 15}, namecolor, COLOR_BLACK, true);
                              
                                        }
                                         //AboveHeadSc.X - 10.5 
                                }
                            }
                            
                         
                            
                                  if (Config.TeamId || Config.Distance)
                                {
                                    
  if (ESPUI1OR2 == 2) {
                                    
                                    FVector BelowRoot = Root;
                                    BelowRoot.Z -= 35.f;
                                    FVector2D BelowRootSc;
                                    if (gGameplayStatics->ProjectWorldToScreen(localPlayerController, BelowRoot, false, &BelowRootSc))
                                    {
                                        std::wstring ws;
                                  if (Config.TeamId)
                                {
                                    
                                
                                            ws += std::to_wstring((int)Player->TeamID);
                                            ws += L"  ";
                                }
                                
                                  if (Config.Distance)
                                {
                                    
                                            ws += L"[";
                                            ws += std::to_wstring((int)Distance);
                                            ws += L"M]";
                                        
                                        }
                                              if (Config.TeamId)
                                {
                                    
                                
                                            
                                            ws += L"  ";
                                }
                                          tslFont->LegacyFontSize = fmax(6.5f, 11.5f - (int)(Distance / 100));
                                        
                                        float txtWidth, txtHeight;
                                        HUD->GetTextSize(Player->PlayerName, tslFont, 1.7f, &txtWidth, &txtHeight);
                                      //  DrawOutlinedText(HUD, FString(ws), FVector2D(BelowRootSc.X, BelowRootSc.Y + 1.7), COLOR_WHITE, COLOR_BLACK, true);
                                       
                                        DrawOutlinedText(HUD, FString(ws), FVector2D(BelowRootSc.X, BelowRootSc.Y + (txtHeight * 0.5f)), COLOR_WHITE, COLOR_BLACK, true);
                               
                                    //    DrawOutlinedText(HUD, FString(ws), {AboveHeadSc.X + 15, AboveHeadSc.Y - 14}, COLOR_YELLOW, COLOR_BLACK, true);
                                    }
                                    }
                                }
            
                                
                            
                                if (Config.Weapon)
                {
                      if (ESPUI1OR2 == 2) {
                                        if (gGameplayStatics->ProjectWorldToScreen(localPlayerController, Head, false, &HeadSc))
                                    {
                                    
                                    std::wstring ws;
                                    
                                 
                if (Config.Weapon)
                {
                                    auto WeaponManagerComponent = Player->WeaponManagerComponent;
                                if(Player->GetCurrentWeapon()){
//auto Sbullet= CurrentWeaponReplicated->CurBulletNumInClip;
                                        auto CurrentWeaponReplicated = (ASTExtraShootWeapon *) WeaponManagerComponent->CurrentWeaponReplicated;
 auto Maxbullet = CurrentWeaponReplicated->CurMaxBulletNumInOneClip;
                    auto CurNum = CurrentWeaponReplicated->CurBulletNumInClip;
                            if(Player->GetCurrentWeapon()->GetWeaponName().IsValid()){
                                
                                
                                      if (Player->bEnsure) {
                      ws +=Player->GetCurrentWeapon()->GetWeaponName().ToWString();;
                                
                        }else{
                       ws +=Player->GetCurrentWeapon()->GetWeaponName().ToWString();;
                                ws += L"&";
                                ws += std::to_wstring((int)CurNum);
    ws += L"/";
    ws += std::to_wstring((int)Maxbullet);
                       
                    }
                    
                                
                                
                        
                            }
                            
                        //    ws += L"\n";
                        }
                                        }
                                        
                                 
                                    auto mWidthScale = std::min(0.006f * Distance, 100.f);
                                    auto mWidth = 20.f - mWidthScale;
                                    auto mHeight = mWidth * 0.125f;
                            //        AboveHeadSc.X -= (mWidth / 2 - 20);
                     //               AboveHeadSc.Y -= (mHeight * 1.4f + 15);
                                    
                            tslFont->LegacyFontSize = fmax(5.5f, 10.5f - (int)(Distance / 100));//4.5f, 10.5f //10.5
                                 DrawOutlinedText(HUD, FString(ws), {HeadSc.X, HeadSc.Y - 40}, colV2, COLOR_BLACK, true);//23.5
                                    //    tslFont->LegacyFontSize = TSL_FONT_DEFAULT_SIZE; //15.5 / 18.5 / 22.5 / 26 / 28
                                     //   tslFont->LegacyFontSize = TSL_FONT_DEFAULT_SIZE;
                                       
                                       // tslFont->LegacyFontSize = fmax(5.5f, 10.5f - (int)(Distance / 100));
                                                //DrawText(HUD, FString(ws), {AboveHeadSc.X - 9, AboveHeadSc.Y - 40},  colV2);
                                        
                                        
                                }
                            }
                            }
                            
                          ////UI 1
                        
                              if (Config.Health)
                {
                      if (ESPUI1OR2 == 0) {
                                   float CurHP = std::max(0.f, std::min(Player->Health, Player->HealthMax));
                                    float MaxHP = Player->HealthMax;
                            
                                    if (CurHP == 0 && !Player->bDead)
                                    {
                                    ColorHP2 = {1.f, 1.f, 0, 0.600f};
                                        CurHP = Player->NearDeathBreath;
                                        USTCharacterNearDeathComp *NearDeatchComponent = Player->NearDeatchComponent;
                                        if (NearDeatchComponent)
                                        {
                                            MaxHP = NearDeatchComponent->BreathMax;
                                        }
                                }
                                
                                auto AboveHead = Player->GetHeadLocation(true);
                                    AboveHead.Z += 35.f;
                                    FVector2D AboveHeadSc;
                                    if (gGameplayStatics->ProjectWorldToScreen(localPlayerController, AboveHead, false, &AboveHeadSc))
                                    {
                                    float mWidth = 80.0f;
                                    float mHeight = -16.0f;
                                    
                                     AboveHeadSc.X -= (mWidth / 2);
                                  AboveHeadSc.Y -= (3.5f * 1.5f);
                                    DrawFilledRectangle(HUD, {AboveHeadSc.X, AboveHeadSc.Y-7}, (CurHP * mWidth / MaxHP), mHeight, ColorHP2);
                                    DrawRectangle(HUD, {AboveHeadSc.X, AboveHeadSc.Y - 7}, mWidth, mHeight, 0.9f, {0, 0, 0, 0.260f});
                                    
}
                                    
                                 }
                            }
                            
                            
                            
                     
                           if (Config.Name)
                                {
                                     if (ESPUI1OR2 == 0) {
                                    auto AboveHead = Player->GetHeadLocation(true);
                                    AboveHead.Z += 35.f;
                                    FVector2D AboveHeadSc;
                                    if (gGameplayStatics->ProjectWorldToScreen(localPlayerController, AboveHead, false, &AboveHeadSc))
                                    {
                                    
                                    std::wstring ws;
                                    std::wstring wsBot;
                                   
                              if (Config.Name)
                                {
//ws = std::to_wstring(Player->TeamID);
                                            auto playername = Player->PlayerName;
                                            if (Player->bEnsure) {
                                    //            ws += E(L" ");
                                             //   ws += L"Bot";
                                         // wsBot += std::to_wstring((int)Player->TeamID);
                                       //   wsBot += E(L" ");
                                            wsBot += E(L"Bot");
                                           //    wsBot += E(L" ");
                                    
                                    //         ws += playername.ToWString();
                                             //  TopNrg = 30.0f;
                                            } else {
                                          
                                                wsBot += playername.ToWString();
                                                
                                                }
                                              //  TopNrg = 30.0f;
                                        }
                                        
                                 
                                    auto mWidthScale = std::min(0.006f * Distance, 100.f);
                                    auto mWidth = 20.f - mWidthScale;
                                    auto mHeight = mWidth * 0.125f;
                                //    AboveHeadSc.X -= (mWidth / 2 - 20);
                                    AboveHeadSc.Y -= (mHeight * 1.4f + 15);
                                        
                                        float Distance2 = Player->GetDistanceTo(localPlayer) / 100.f;
                                    
                                 tslFont->LegacyFontSize = fmax(6, 10.0f - (Distance2 / 100));//4.5f, 10.5f //10.5
                                 
                              
                              //   tslFont->LegacyFontSize = 12;
      //  DrawText(HUD, FString(wsBot), {AboveHeadSc.X, AboveHeadSc.Y - 2},  COLOR_WHITE);
     
                             //    HeadSc.X -= (mWidth / 2);
                              //       AboveHeadSc.Y -= (mHeight * 1.5f);
                             DrawOutlinedText(HUD, FString(wsBot), {AboveHeadSc.X, AboveHeadSc.Y - 2}, COLOR_WHITE, COLOR_BLACK, true);
                              
                                 //tslFont->LegacyFontSize = 12;
    //    DrawText(HUD, FString(wsBot), {AboveHeadSc.X, AboveHeadSc.Y - 6.2},  COLOR_WHITE);
     
                                 //   DrawOutlinedTextNRG
                                    //    tslFont->LegacyFontSize = TSL_FONT_DEFAULT_SIZE; //15.5 / 18.5 / 22.5 / 26 / 28
                                     //   tslFont->LegacyFontSize = TSL_FONT_DEFAULT_SIZE;
                                        
                                        }
                                         //AboveHeadSc.X - 10.5 
                                }
                            }
                            
                           
                            
                            
                            
                            
                                  if (Config.Distance)
                                {
                                    
  if (ESPUI1OR2 == 0) {
                                    
                                    auto AboveHead = Player->GetHeadLocation(true);
                                    AboveHead.Z += 35.f;
                                    FVector2D AboveHeadSc;
                                    if (gGameplayStatics->ProjectWorldToScreen(localPlayerController, AboveHead, false, &AboveHeadSc))
                                    {
                                        std::wstring ws;
                                
                                            ws += L"[";
                                            ws += std::to_wstring((int)Distance);
                                            ws += L"M]";
                                        
                                        
                                        
                                          auto mWidthScale = std::min(0.006f * Distance, 100.f);
                                    auto mWidth = 20.f - mWidthScale;
                                    auto mHeight = mWidth * 0.125f;
                                //    AboveHeadSc.X -= (mWidth / 2 - 20);
                                    AboveHeadSc.Y -= (mHeight * 1.4f + 20);
                                        
                                        float Distance2 = Player->GetDistanceTo(localPlayer) / 100.f;
                                    
                                        tslFont->LegacyFontSize = fmax(6, 9.5f - (Distance2 / 100));//4.5f, 10.5f //10.5
                                 
                                        
                                        DrawOutlinedText(HUD, FString(ws), {AboveHeadSc.X + 15, AboveHeadSc.Y - 14}, COLOR_YELLOW, COLOR_BLACK, true);
                                    }
                                    }
                                }
            
                                
                                
                if (Config.TeamId)
                {
                                      if (ESPUI1OR2 == 0) {

                                    
                                    auto AboveHead = Player->GetHeadLocation(true);
                                    AboveHead.Z += 35.f;
                                    FVector2D AboveHeadSc;
                                    if (gGameplayStatics->ProjectWorldToScreen(localPlayerController, AboveHead, false, &AboveHeadSc))
                                    {
                                        std::wstring ws;
                                    
                                            ws += L"[";
                                            ws += std::to_wstring((int)Player->TeamID);
                                            ws += L"]";
                                        
                                        
                                        
                                          auto mWidthScale = std::min(0.006f * Distance, 100.f);
                                    auto mWidth = 20.f - mWidthScale;
                                    auto mHeight = mWidth * 0.125f;
                                //    AboveHeadSc.X -= (mWidth / 2 - 20);
                                    AboveHeadSc.Y -= (mHeight * 1.4f + 20);
                                        
                                        float Distance2 = Player->GetDistanceTo(localPlayer) / 100.f;
                                    
                                        tslFont->LegacyFontSize = fmax(6, 9.5f - (Distance2 / 100));//4.5f, 10.5f //10.5
                                 
                                        
                                        DrawOutlinedText(HUD, FString(ws), {AboveHeadSc.X - 24, AboveHeadSc.Y - 14}, COLOR_LIME, COLOR_BLACK, true);
                                    }
                                }
                                }
                            
                                              if (Config.Weapon)
                                {
                                      if (ESPUI1OR2 == 0) {
                                    auto AboveHead = Player->GetHeadLocation(true);
                                    AboveHead.Z += 35.f;
                                    FVector2D AboveHeadSc;
                                    if (gGameplayStatics->ProjectWorldToScreen(localPlayerController, AboveHead, false, &AboveHeadSc))
                                    {
                                    
                                    std::wstring ws;
                                    
                                      if (Config.Weapon)
                                {
                                    auto WeaponManagerComponent = Player->WeaponManagerComponent;
                                if(Player->GetCurrentWeapon()){
//auto Sbullet= CurrentWeaponReplicated->CurBulletNumInClip;
                                        auto CurrentWeaponReplicated = (ASTExtraShootWeapon *) WeaponManagerComponent->CurrentWeaponReplicated;
 auto Maxbullet = CurrentWeaponReplicated->CurMaxBulletNumInOneClip;
                    auto CurNum = CurrentWeaponReplicated->CurBulletNumInClip;
                            if(Player->GetCurrentWeapon()->GetWeaponName().IsValid()){
                                
                                
                                      if (Player->bEnsure) {
                      ws +=Player->GetCurrentWeapon()->GetWeaponName().ToWString();;
                                
                        }else{
                       ws +=Player->GetCurrentWeapon()->GetWeaponName().ToWString();;
                                ws += L" ";
                                ws += std::to_wstring((int)CurNum);
    ws += L"/";
    ws += std::to_wstring((int)Maxbullet);
                       
                    }
                    
                                
                                
                        
                            }
                            
                        //    ws += L"\n";
                        }
                                        }
                                        
                                 
                                    auto mWidthScale = std::min(0.006f * Distance, 100.f);
                                    auto mWidth = 20.f - mWidthScale;
                                    auto mHeight = mWidth * 0.125f;
                               //     AboveHeadSc.X -= (mWidth / 2 - 20);
                                    AboveHeadSc.Y -= (mHeight * 1.4f + 15);
                                    
                                    
                                    float Distance2 = Player->GetDistanceTo(localPlayer) / 100.f;
                                    
                            tslFont->LegacyFontSize = fmax(5.5f, 9.5f - (Distance2 / 100));//4.5f, 10.5f //10.5
                                 DrawOutlinedText(HUD, FString(ws), {AboveHeadSc.X, AboveHeadSc.Y+ 13}, {0.2f, 0.8f, 1.f, 1.f}, COLOR_BLACK, true);//23.5

                                        
                                }
                            }}
                                
                                
                                
                            
                            
                            /*
                            ///ESP UI2
                                       if (Config.Health)
                {
                      if (ESPUI1OR2 == 2) {
                                   float CurHP = std::max(0.f, std::min(Player->Health, Player->HealthMax));
                                    float MaxHP = Player->HealthMax;
                            
                                    if (CurHP == 0 && !Player->bDead)
                                    {
                                    ColorHP = {1.f, 1.f, 0, 1.f};
                                        CurHP = Player->NearDeathBreath;
                                        USTCharacterNearDeathComp *NearDeatchComponent = Player->NearDeatchComponent;
                                        if (NearDeatchComponent)
                                        {
                                            MaxHP = NearDeatchComponent->BreathMax;
                                        }
                                }
                                
                                auto AboveHead = Player->GetHeadLocation(true);
                                    AboveHead.Z += 35.f;
                                    FVector2D AboveHeadSc;
                                    if (gGameplayStatics->ProjectWorldToScreen(localPlayerController, AboveHead, false, &AboveHeadSc))
                                    {
                                    
                                        
                                    
                                    
                             //     auto mWidthScale = std::min(0.0f * Distance, 20.f);//27 
                                // i use  
                               auto mWidthScale = std::min(0.0f * 24.0f, 0.0f); //100  75 50 | 24
                                //    auto mWidthScale = std::min(0.10f * Distance, 50.f);

                                    
                                    auto mWidth = 40.0f - mWidthScale;
                                   
                                    
                                 //   float boxHeight = abs(HeadSc.Y - RootSc.Y);
                                    auto mHeight = mWidth * 0.070f;//098f //0.07f
                                    auto mHeight2 = abs(HeadSc.Y - RootSc.Y);//098f //0.07f
                           //         auto mHeightgg = mWidth * -DanceValue;//098f //0.07f
                                //    auto mHeight2 = mWidth * 0.140f; //180f
                                    AboveHeadSc.X -= (mWidth / 2);
                                  AboveHeadSc.Y -= (mHeight * 1.5f);
                                  
                                    DrawFilledRectangle(HUD, {AboveHeadSc.X, AboveHeadSc.Y - 7}, (CurHP * mWidth / MaxHP), mHeight2, ColorHP);
                           
                                    }
                                    
                                 }
                            }
                            */
                if (Config.Health)
                {
                      if (ESPUI1OR2 == 1) {
                                   float CurHP = std::max(0.f, std::min(Player->Health, Player->HealthMax));
                                    float MaxHP = Player->HealthMax;
                            
                                    if (CurHP == 0 && !Player->bDead)
                                    {
                                    ColorHP = {1.f, 1.f, 0, 1.f};
                                        CurHP = Player->NearDeathBreath;
                                        USTCharacterNearDeathComp *NearDeatchComponent = Player->NearDeatchComponent;
                                        if (NearDeatchComponent)
                                        {
                                            MaxHP = NearDeatchComponent->BreathMax;
                                        }
                                }
                                
                                auto AboveHead = Player->GetHeadLocation(true);
                                    AboveHead.Z += 35.f;
                                    FVector2D AboveHeadSc;
                                    if (gGameplayStatics->ProjectWorldToScreen(localPlayerController, AboveHead, false, &AboveHeadSc))
                                    {
                                    
                                        
                                    
                                    
                             //     auto mWidthScale = std::min(0.0f * Distance, 20.f);//27 
                                // i use  
                               auto mWidthScale = std::min(0.0f * 24.0f, 0.0f); //100  75 50 | 24
                                //    auto mWidthScale = std::min(0.10f * Distance, 50.f);

                                    auto mWidth2 = 10.0f - mWidthScale;
                                    auto mWidth3 = 20.0f - mWidthScale;
                                    auto mWidth4 = 30.0f - mWidthScale;
                                    auto mWidth5 = 40.0f - mWidthScale;
                                    auto mWidth = 50.0f - mWidthScale;
                                    auto mWidth99 = 5.0f - mWidthScale;
                                    
                                    
                                 //   float boxHeight = abs(HeadSc.Y - RootSc.Y);
                                    auto mHeight = mWidth * 0.070f;//098f //0.07f
                                    auto mHeightgg = mWidth * -DanceValue;//098f //0.07f
                                //    auto mHeight2 = mWidth * 0.140f; //180f
                                    AboveHeadSc.X -= (mWidth / 2);
                                  AboveHeadSc.Y -= (mHeight * 1.5f);
                                  
                                  
                                   
                          //      DrawFilledRectangle(HUD, {AboveHeadSc.X, AboveHeadSc.Y - 7}, mWidth, mHeightgg, GetRandomColorByIndexHud(Player->TeamID));
                                
                                    DrawFilledRectangle(HUD, {AboveHeadSc.X, AboveHeadSc.Y - 7}, (CurHP * mWidth / MaxHP), mHeight, ColorHP);
                                    
                                 //   DrawFilledRectangle(HUD, {AboveHeadSc.X, AboveHeadSc.Y - 7}, mHeight, (CurHP * mWidth / MaxHP), ColorHP);
                                    
//DrawFilledRectangle(HUD, {AboveHeadSc.X, AboveHeadSc.Y - 7}, (CurHP * mWidth / MaxHP), mHeight, ColorHP);
                
                                  
                                    ///////    
                            /////////
                              
                                    DrawRectangle(HUD, {AboveHeadSc.X, AboveHeadSc.Y - 7}, mWidth2, mHeight, 1.0f, {0, 0, 0, 1.f});
                                    DrawRectangle(HUD, {AboveHeadSc.X, AboveHeadSc.Y - 7}, mWidth3, mHeight, 1.0f, {0, 0, 0, 1.f});
                                    DrawRectangle(HUD, {AboveHeadSc.X, AboveHeadSc.Y - 7}, mWidth4, mHeight, 1.0f, {0, 0, 0, 1.f});
                                    DrawRectangle(HUD, {AboveHeadSc.X, AboveHeadSc.Y - 7}, mWidth5, mHeight, 1.0f, {0, 0, 0, 1.f});
                                    DrawRectangle(HUD, {AboveHeadSc.X, AboveHeadSc.Y - 7}, mWidth, mHeight, 1.0f, {0, 0, 0, 1.f});
                                    }
                                    
                                 }
                            }
                                         
                            
                        
                           
                           
                            /*
                            //绘制手持
                            if (Config.PlayerESP.Weapon) {
                                auto AboveHead = Player->GetHeadLocation(true);
                                AboveHead.Z += 35.f;
                                FVector2D AboveHeadSc;
                                if (gGameplayStatics->ProjectWorldToScreen(localController, AboveHead, false, &AboveHeadSc)) {
                                    auto WeaponManagerComponent = Player->WeaponManagerComponent;
                                    if (WeaponManagerComponent) {
                                        auto CurrentWeaponReplicated = (ASTExtraShootWeapon *)WeaponManagerComponent->CurrentWeaponReplicated;
                                        if (CurrentWeaponReplicated) {
                                            auto WeaponId = (int)CurrentWeaponReplicated->GetWeaponID();
                                            if (WeaponId) {
                                                std::string s;
                                                s += CurrentWeaponReplicated->GetWeaponName().ToString();
                                                tslFont->LegacyFontSize = fmax(5.5f, 10.5f - (int)(Distance / 100));
                                                DrawText(Canvas, FString(s), {HeadSc.X - 9, AboveHeadSc.Y - 40},  COLOR_WHITE);
                                            }
                                        }
                                    }
                                }
                            }
                            */
                if (Config.Weapon)
                {
                      if (ESPUI1OR2 == 1) {
                                    auto AboveHead = Player->GetHeadLocation(true);
                                    AboveHead.Z += 35.f;
                                    FVector2D AboveHeadSc;
                                    if (gGameplayStatics->ProjectWorldToScreen(localPlayerController, AboveHead, false, &AboveHeadSc))
                                    {
                                    
                                    std::wstring ws;
                                    
                                 
                if (Config.Weapon)
                {
                                    auto WeaponManagerComponent = Player->WeaponManagerComponent;
                                if(Player->GetCurrentWeapon()){
//auto Sbullet= CurrentWeaponReplicated->CurBulletNumInClip;
                                        auto CurrentWeaponReplicated = (ASTExtraShootWeapon *) WeaponManagerComponent->CurrentWeaponReplicated;
 auto Maxbullet = CurrentWeaponReplicated->CurMaxBulletNumInOneClip;
                    auto CurNum = CurrentWeaponReplicated->CurBulletNumInClip;
                            if(Player->GetCurrentWeapon()->GetWeaponName().IsValid()){
                                
                                
                                      if (Player->bEnsure) {
                      ws +=Player->GetCurrentWeapon()->GetWeaponName().ToWString();;
                                
                        }else{
                       ws +=Player->GetCurrentWeapon()->GetWeaponName().ToWString();;
                                ws += L"&";
                                ws += std::to_wstring((int)CurNum);
    ws += L"/";
    ws += std::to_wstring((int)Maxbullet);
                       
                    }
                    
                                
                                
                        
                            }
                            
                        //    ws += L"\n";
                        }
                                        }
                                        
                                 
                                    auto mWidthScale = std::min(0.006f * Distance, 100.f);
                                    auto mWidth = 20.f - mWidthScale;
                                    auto mHeight = mWidth * 0.125f;
                                    AboveHeadSc.X -= (mWidth / 2 - 20);
                                    AboveHeadSc.Y -= (mHeight * 1.4f + 15);
                                    
                            tslFont->LegacyFontSize = fmax(5.5f, 10.5f - (int)(Distance / 100));//4.5f, 10.5f //10.5
                                 DrawOutlinedText(HUD, FString(ws), {AboveHeadSc.X - 11.2, AboveHeadSc.Y - 21.8}, colV2, COLOR_BLACK, true);//23.5
                                    //    tslFont->LegacyFontSize = TSL_FONT_DEFAULT_SIZE; //15.5 / 18.5 / 22.5 / 26 / 28
                                     //   tslFont->LegacyFontSize = TSL_FONT_DEFAULT_SIZE;
                                       
                                       // tslFont->LegacyFontSize = fmax(5.5f, 10.5f - (int)(Distance / 100));
                                                //DrawText(HUD, FString(ws), {AboveHeadSc.X - 9, AboveHeadSc.Y - 40},  colV2);
                                        
                                        
                                }
                            }
                            }
                            
                           
                              if (Config.Name)
                                {
                                  
                                 FVector BelowRoot = Player->GetBonePos("pelvis", {});
                                FVector2D BelowRootSc;
                                if (gGameplayStatics->ProjectWorldToScreen(localPlayerController, BelowRoot, false, &BelowRootSc)) {
                                        if (Player->IsInvincible){
                                 //       IntCount ++;
                                        std::wstring spwanCounter;
                                        spwanCounter += L"Respawing: ";
                                        spwanCounter += std::to_wstring(IntCount);
                                     //     s += std::to_string(totalEnemies);
                                    //    DrawSmallOutlinedText(HUD,spwanCounter, FVector2D(BelowRootSc.X, BelowRootSc.Y ), visCol, COLOR_BLUE, true);
                                        
                                        
                                          tslFont->LegacyFontSize = fmax(5.5f, 10.5f - (int)(Distance / 100));
                                        
                                 DrawOutlinedText(HUD, spwanCounter, {BelowRootSc.X, BelowRootSc.Y}, visCol, COLOR_BLACK, true);
                                        tslFont->LegacyFontSize = TSL_FONT_DEFAULT_SIZE;
                                    }else{
                                 //       IntCount = 0;
                                    }
                                    std::wstring ws_msg ;
                                    if(Player->IsRescueingOther){
                                        ws_msg = L"Reviveing";
                                        tslFont->LegacyFontSize = fmax(5.5f, 10.5f - (int)(Distance / 100));
                                        
                                 DrawOutlinedText(HUD, ws_msg, {BelowRootSc.X, BelowRootSc.Y+40}, visCol, COLOR_BLACK, true);
                                        tslFont->LegacyFontSize = TSL_FONT_DEFAULT_SIZE;
                                        
                                        
                                     //   tslFont->LegacyFontSize = 14;
                                      }
                                        //DrawOutlinedText(HUD, FString(ws_msg),FVector2D(BelowRootSc.X, BelowRootSc.Y+40 ), visCol, COLOR_BLACK, true);
                                    }
                                    }
                                    
                      /*
if (Config.PlayerESP.EspInfo) {
//FVector2D FlagL = {HeadSc.X - 67.5, HeadSc.Y - 57.5};
//ImVec2 FlagR = ImVec2(HeadPosSC.x - 35, HeadPosSC.y - 37.5);
//draw->AddRectFilled(FlagL, FlagR,IM_COL32(255,255,255,70), 2.0f); 

DrawFilledRectangle(HUD, {HeadSc.X - 67.5, HeadSc.Y - 57.5}, HeadSc.X - 35, HeadSc.Y - 37.5, {1.f, 1.f, 1.f, 0.10f});

std::string s;
if (Config.PlayerESP.EspInfo) {
s += "";
s += Player->Nation.ToString(); 
//draw->AddText(pubg_font,24,ImVec2(HeadPosSC.x - 60, HeadPosSC.y - 60), IM_COL32(255, 255, 0, 255),s.c_str());
tslFont->LegacyFontSize = fmax(5.0f, 10.0f - (int)(Distance / 100));
DrawOutlinedTextNRG(HUD, FString(s), {HeadSc.X - 60, HeadSc.Y - 60}, {1.f, 1.f, 0.f, 1.f}, true);
tslFont->LegacyFontSize = TSL_FONT_DEFAULT_SIZE;
}}

if (Config.PlayerESP.EspInfo) {
std::string s;
int max_length = 25;
if (Config.PlayerESP.EspInfo) {
if (Player->bEnsure) {
s += " Bot";
} else {
s += "";
s += Player->PlayerName.ToString();
if (s.length() > 8) {
s = s.substr(0, 8);
}
s += ".."; // Добавляем многоточие для обозначения усечения
}
tslFont->LegacyFontSize = fmax(5.0f, 10.0f - (int)(Distance / 100));
DrawOutlinedTextNRG(HUD, FString(s), {HeadSc.X - 30, HeadSc.Y - 60}, colV2, true);
tslFont->LegacyFontSize = TSL_FONT_DEFAULT_SIZE;
//draw->AddText(pubg_font,24,ImVec2(HeadPosSC.x - 30, HeadPosSC.y - 60), IM_COL32(255, 255, 255, 255),s.c_str());
}}
                       */             
                       
                            
                                     if (Config.TeamId || Config.Name)
                                {
                                      if (ESPUI1OR2 == 1) {
                                   
                                    auto AboveHead = Player->GetHeadLocation(true);
                                    AboveHead.Z += 35.f;
                                    FVector2D AboveHeadSc;
                                    if (gGameplayStatics->ProjectWorldToScreen(localPlayerController, AboveHead, false, &AboveHeadSc))
                                    {
                                    
                                    std::wstring ws;
                                    std::wstring wsBot;
                                      if (Config.Name)
                                        {
//ws = std::to_wstring(Player->TeamID);
                                            auto playername = Player->PlayerName;
                                            if (Player->bEnsure) {
                                    //            ws += E(L" ");
                                             //   ws += L"Bot";
                                         // wsBot += std::to_wstring((int)Player->TeamID);
                                         
                                       //   wsBot += E(L" ");
                                            wsBot += E(L"Bot");
                                           //    wsBot += E(L" ");
                                    
                                    //         ws += playername.ToWString();
                                             //  TopNrg = 30.0f;
                                            } else {
                                                //if (playername.IsValid())
                                            //  ws += E(L" ");
                                                 if (Config.TeamId)
                                        {
                                                ws += std::to_wstring((int)Player->TeamID);
                                                ws += E(L" ");
                                                }
                                                ws += playername.ToWString();
                                                ws += E(L"  ");
                                                }
                                              //  TopNrg = 30.0f;
                                        }
                                        
                                    
                                        //    ws += E(L"");
                                
                                 
                                    auto mWidthScale = std::min(0.006f * Distance, 100.f);
                                    auto mWidth = 20.f - mWidthScale;
                                    auto mHeight = mWidth * 0.125f;
                                //    AboveHeadSc.X -= (mWidth / 2 - 20);
                                    AboveHeadSc.Y -= (mHeight * 1.4f + 15);
                       //     tslFont->LegacyFontSize = fmax(4.5f, 9.5f - (int)(Distance / 100));//5.0f, 11.0f //8.0
                       
                       
                       
                                      //5.5f, 10.5f /  - 7.0
                            tslFont->LegacyFontSize = fmax(5.5f, 10.5f - (int)(Distance / 100));//4.5f, 10.5f //10.5
                                 DrawOutlinedText(HUD, FString(ws), {AboveHeadSc.X, AboveHeadSc.Y - 6.5}, colV2, COLOR_BLACK, true);
                     tslFont->LegacyFontSize = TSL_FONT_DEFAULT_SIZE;
                                 
                                 tslFont->LegacyFontSize = fmax(5.5f, 10.5f - (int)(Distance / 100));//4.5f, 10.5f //10.5
                                DrawOutlinedText(HUD, FString(wsBot), {AboveHeadSc.X, AboveHeadSc.Y - 6.5}, colV2, COLOR_BLACK, true);
                              
                                 
                                 //   DrawOutlinedTextNRG
                                    //    tslFont->LegacyFontSize = TSL_FONT_DEFAULT_SIZE; //15.5 / 18.5 / 22.5 / 26 / 28
                                     //   tslFont->LegacyFontSize = TSL_FONT_DEFAULT_SIZE;
                                        
                                        
                                         //AboveHeadSc.X - 10.5 
                                }
                            }
                            
                     }
                   
                                
                       //绘制信息
                           /*
                                     if (Config.PlayerESP.EspInfo)
                                {
                                   
                                auto AboveHead = Player->GetHeadLocation(true);
                                AboveHead.Z += 35.f;
                                FVector2D AboveHeadSc;
                                if (gGameplayStatics->ProjectWorldToScreen(localPlayerController, AboveHead, false, &AboveHeadSc)) {
                                    std::wstring ws;
                                 
                                     if (Config.PlayerESP.EspInfo)
                                {
                                   
                                        
                                        auto playername = Player->PlayerName;
                                        if (Player->bEnsure) {
                                         //   ws += L" ";
                                            ws += L"Bot";
                                        } else {
                                            ws = std::to_wstring(Player->TeamID);
                                            ws += L" ";
                                            ws += playername.ToWString();
                                        }
                                    }
                                    auto mWidthScale = std::min(0.006f * Distance, 100.f);
                                    auto mWidth = 20.f - mWidthScale;
                                    auto mHeight = mWidth * 0.125f;
                                    AboveHeadSc.X -= (mWidth / 2 - 20);
                                    AboveHeadSc.Y -= (mHeight * 1.4f + 15);
                                    tslFont->LegacyFontSize = fmax(5.5f, 10.5f - (int)(Distance / 100));
                                    DrawText(HUD, FString(ws), {AboveHeadSc.X - 9, AboveHeadSc.Y - 7.0},  colV2);
                                }
                            }
            
           */
            
            
                            if (Config.Distance)
                                {
                                      if (ESPUI1OR2 == 1) {

                                    
                                    FVector BelowRoot = Root;
                                    BelowRoot.Z -= 35.f;
                                    FVector2D BelowRootSc;
                                    if (gGameplayStatics->ProjectWorldToScreen(localPlayerController, BelowRoot, false, &BelowRootSc))
                                    {
                                        std::wstring ws;
                                     
                                            ws += L"";
                                            ws += std::to_wstring((int)Distance);
                                            ws += L"m   ";
                                        
                                        tslFont->LegacyFontSize = fmax(6.5f, 11.5f - (int)(Distance / 100));
                                        
                                        float txtWidth, txtHeight;
                                        HUD->GetTextSize(Player->PlayerName, tslFont, 1.7f, &txtWidth, &txtHeight);
                                      //  DrawOutlinedText(HUD, FString(ws), FVector2D(BelowRootSc.X, BelowRootSc.Y + 1.7), COLOR_WHITE, COLOR_BLACK, true);
                                       
                                        DrawOutlinedText(HUD, FString(ws), FVector2D(BelowRootSc.X, BelowRootSc.Y + (txtHeight * 0.5f)), COLOR_WHITE, COLOR_BLACK, true);
                                        //tslFont->LegacyFontSize = TSL_FONT_DEFAULT_SIZE;
                                     //   tslFont->LegacyFontSize = TSL_FONT_DEFAULT_SIZE;
                                    }
                                }}
            
          
                                
                                
                               
                            }
                    /*
                   
                  if (localPlayer->PartHitComponent) {
            auto ConfigCollisionDistSqAngles = localPlayer->PartHitComponent->ConfigCollisionDistSqAngles;
            auto numAngles = ConfigCollisionDistSqAngles.Num();
            for (int j = numAngles - 1; j >= 0; j--) {
            int numAnglesIndex = numAngles - j -1;
            float offset = sin(j* 0.2f + 0.3f)* 2.0f;
            float baseAngle = numAnglesIndex % 2 == 0 ? 170.0f : -190.0f;
            ConfigCollisionDistSqAngles[j].Angle = baseAngle + offset;
            localPlayer->PartHitComponent->ConfigCollisionDistSqAngles = ConfigCollisionDistSqAngles;
            }}
            */
                 if (localPlayer->PartHitComponent) {
                        auto ConfigCollisionDistSqAngles = localPlayer->PartHitComponent->ConfigCollisionDistSqAngles;
                        for (int j = 0; j < ConfigCollisionDistSqAngles.Num(); j++) {
                            ConfigCollisionDistSqAngles[j].Angle = 180.0f;
                        }
                        localPlayer->PartHitComponent->ConfigCollisionDistSqAngles = ConfigCollisionDistSqAngles;
                    }
                
                    
                    /*
                      if (Config.AimBot.Enable)
                    {
                            if (Config.AimEnable == 1) {
                                
                                auto WeaponManagerComponent = localPlayer->WeaponManagerComponent;
    if (WeaponManagerComponent) {
        auto propSlot = WeaponManagerComponent->GetCurrentUsingPropSlot();
        if ((int)propSlot.GetValue() >= 1 && (int)propSlot.GetValue() <= 3) {
            auto CurrentWeaponReplicated = (ASTExtraShootWeapon*)WeaponManagerComponent->CurrentWeaponReplicated;
            if (CurrentWeaponReplicated) {
                auto ShootWeaponComponent = CurrentWeaponReplicated->ShootWeaponComponent;
                if (ShootWeaponComponent) {
                    int shoot_event_idx = 168;
                    auto VTable = *(void ***) (ShootWeaponComponent);
                    auto f_mprotect = [](uintptr_t addr, size_t len, int32_t prot) -> int32_t {
                      static_assert(PAGE_SIZE == 4096);
                     constexpr size_t page_size = static_cast<size_t>(PAGE_SIZE);
                      void* start = reinterpret_cast<void*>(addr & -page_size);
                      uintptr_t end = (addr + len + page_size - 1) & -page_size;
                      return mprotect(start, end - reinterpret_cast<uintptr_t>(start), prot);
                   };
                   if(VTable){
                    uintptr_t table_entry = reinterpret_cast<uintptr_t>(&VTable[shoot_event_idx]); 
                    if (VTable && (VTable[shoot_event_idx] != shoot_event)) {                      
                        orig_shoot_event = decltype(orig_shoot_event)(VTable[shoot_event_idx]);
                        f_mprotect((uintptr_t)(&VTable[shoot_event_idx]), sizeof(uintptr_t), PROT_READ | PROT_WRITE);                                                          
                        uintptr_t new_func_ptr = reinterpret_cast<uintptr_t>(&shoot_event);
                        VTable[shoot_event_idx] = reinterpret_cast<void*>(new_func_ptr);
                        f_mprotect(table_entry, sizeof(uintptr_t), PROT_READ | PROT_EXEC);
                        }
                    }
                }
            }
        }
    }
    
    
    }}
    */
    
    

       if (Config.AimBotOpen) {
                            if (Config.AimBot.Enable) {
                                     //     if (Config.AimEnable == 0) {
             //                            float AimSpeed2;
                                    ASTExtraPlayerCharacter *Target = GetTargetForAimBot();
                            
                               if (Target) {
                        bool triggerOk = false;
                       //    if (NRGtrigger == 4) {
                              if (Config.NRGtrigger == 0) {
                               triggerOk = localPlayer->bIsWeaponFiring;
                            //   triggerOk = localPlayer->bIsWeaponFiring || localPlayer->bIsGunADS;
                          } else  if (Config.NRGtrigger == 1) {
                                triggerOk = localPlayer->bIsGunADS;
                             //   triggerOk = localPlayer->bIsWeaponFiring || localPlayer->bIsGunADS;
                                } else   if (Config.NRGtrigger == 2) {
                                triggerOk = localPlayer->bIsWeaponFiring || localPlayer->bIsGunADS;
                    //            }
                                } else triggerOk = true;
                     if (triggerOk) {
                             //  if (localPlayer->bIsWeaponFiring || localPlayer->bIsGunADS) {
                                        FVector targetAimPos = Target->GetBonePos("Head", {});
                
//if (Config.AimBot.Lessmmm) {
                                        
                                                    if (localPlayer->bIsGunADS) {
                                                        if (localPlayer->bIsWeaponFiring) {
                                                            float dist = localPlayer->GetDistanceTo(Target) / 100.f;                                                                                 
                                                            targetAimPos.Z -= dist * Config.AimBot.Recc;
                                                        }  
                                                    }
                                                    
                                                    
                                              
                                                            
                                            auto WeaponManagerComponent = localPlayer->WeaponManagerComponent;
                                            if (WeaponManagerComponent) {
                                                auto propSlot = WeaponManagerComponent->GetCurrentUsingPropSlot();
                                                if ((int) propSlot.GetValue() >= 1 &&
                                                    (int) propSlot.GetValue() <= 3) {
                                                    auto CurrentWeaponReplicated = (ASTExtraShootWeapon *) WeaponManagerComponent->CurrentWeaponReplicated;
                                                    if (CurrentWeaponReplicated) {
                                                        auto ShootWeaponComponent = CurrentWeaponReplicated->ShootWeaponComponent;
                                                        if (ShootWeaponComponent) {
                                                            UShootWeaponEntity *ShootWeaponEntityComponent = ShootWeaponComponent->ShootWeaponEntityComponent;
                                                            if (ShootWeaponEntityComponent) {
                                                                ASTExtraVehicleBase *CurrentVehicle = Target->CurrentVehicle;
                                                                if (CurrentVehicle) {
                                                                    FVector LinearVelocity = CurrentVehicle->ReplicatedMovement.LinearVelocity;
                                                                    float dist = localPlayer->GetDistanceTo(Target);
                                                                    auto timeToTravel = dist /
                                                                    ShootWeaponEntityComponent->BulletFireSpeed;
                                                                    targetAimPos = UMC->Add_VectorVector(targetAimPos,UMC->Multiply_VectorFloat(LinearVelocity, timeToTravel));
                                                                } else {
                                                                    FVector Velocity = Target->GetVelocity();
                                                                    float dist = localPlayer->GetDistanceTo(Target);
                                                                    auto timeToTravel = dist /
                                                                    ShootWeaponEntityComponent->BulletFireSpeed;
                                                                    targetAimPos = UMC->Add_VectorVector(targetAimPos, UMC->Multiply_VectorFloat(Velocity, timeToTravel));
                                                                }
                                                                
                   /*                                             
                                                                
                                                                
               if (aimbotspeednrg == 0) {
AimSpeed2 = 14.f;
}else     if (aimbotspeednrg == 1) {
AimSpeed2= 5.f; //6
}else     if (aimbotspeednrg == 2) {
AimSpeed2 = 1.f;
}else     if (aimbotspeednrg == 3) {
AimSpeed2 = AimCustomSpeed;
}
                                   
                                                
                                                           
                            if (Config.AimBot.Enable) {
                                            if (localPlayerController) {
                                            auto ControlRotator =  localPlayerController->ControlRotation;      
                                            auto PlayerCameraManage = localPlayerController->PlayerCameraManager;
                                            if (PlayerCameraManage) {                                                                                                                                          
                                                FVector currViewAngle = PlayerCameraManage->CameraCache.POV.Location;                                                
                                                auto aimRotation = ToRotator(currViewAngle, targetAimPos);                                                    
                                            
                            if (Config.AimBot.Enable) {
                                                aimRotation.Yaw -= ControlRotator.Yaw;
                                                aimRotation.Pitch -= ControlRotator.Pitch;
                                                AimAngle(aimRotation);                                                
                                                ControlRotator.Pitch += aimRotation.Pitch / (float) AimSpeed2;
                                                ControlRotator.Yaw += aimRotation.Yaw / (float) AimSpeed2;
                                                }else{
                                                ControlRotator.Yaw = aimRotation.Yaw;
                                                ControlRotator.Pitch = aimRotation.Pitch;
                                                 }                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         
                                                localPlayerController->ControlRotation = ControlRotator;                                              
                                            }
                                            }
                                            }       
                                            */
                                                            localPlayerController->ClientSetLocation(localPlayer->K2_GetActorLocation(), ToRotator(localPlayerController->PlayerCameraManager->CameraCache.POV.Location, targetAimPos));

         
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }}

                    
                            
                        }
                        
                     
                        
                        
                        /*
                        
                                 if (Config.PlayerESP.Alert)
                            {
                                //       if (Config.Alert12 == 0) {
                                    auto AboveHead = Player->GetHeadLocation(true);
                                    
                                    FVector2D AboveHeadSc;
                               bool Useless = false;
                                FVector2D EntityPos = WorldToRadar(localPlayer->K2_GetActorRotation().Yaw, AboveHead, localPlayer->GetHeadLocation(true), NULL, NULL, FVector((float)screenWidth, (float)screenHeight, 0.f), Useless);
                                float radar_range = 200.f;//270
                                FVector angle;
                                FVector MiddlePoint(((float)screenWidth / 2.f) - EntityPos.X, ((float)screenHeight / 2.f) - EntityPos.Y, 0.f);
                                VectorAnglesRadar(MiddlePoint, angle);
                                const auto AngleYawRadian = DEG2RAD(angle.Y + 180.f);
                                float Point_X = ((float)screenWidth / 2.f) + (radar_range) / 2.f * 8.f * cosf(AngleYawRadian);
                                float Point_Y = ((float)screenHeight / 2.f) + (radar_range) / 2.f * 8.f * sinf(AngleYawRadian);
                                FixTriangle(Point_X, Point_Y, 100);
                                std::array < FVector2D, 39 > points
                                {
                                    
                                    
                                    FVector2D((float)Point_X - (5.6f * (float)2.f), Point_Y - (7.3f *2.f)),
                  FVector2D((float)Point_X + (11.6f *2.f), Point_Y),
                  FVector2D((float)Point_X - (5.6f *2.f), Point_Y + (7.3f *2.f)),
                  FVector2D((float)Point_X - (5.6f *2.f), Point_Y - (4.3f *2.f)),
                  FVector2D((float)Point_X - (19.5f *2.f), Point_Y - (4.3f *2.f)),
                  FVector2D((float)Point_X - (19.5f *2.f), Point_Y + (4.3f *2.f)),
                  FVector2D((float)Point_X - (5.6f *2.f), Point_Y + (4.3f *2.f)),
                  
                  FVector2D((float)Point_X + (10.3f *2.f), Point_Y),
                  FVector2D((float)Point_X - (5.f *2.f), Point_Y - (3.5f *2.f)),
                  FVector2D((float)Point_X - (5.f *2.f), Point_Y + (3.5f *2.f)),
                  
                  FVector2D((float)Point_X - (5.f * (float)2.f), Point_Y - (6.5f *2.f)),
                  FVector2D((float)Point_X - (5.f * (float)2.f), Point_Y - (5.5f *2.f)),
                  FVector2D((float)Point_X - (5.f * (float)2.f), Point_Y - (4.5f *2.f)),
                  FVector2D((float)Point_X - (5.f * (float)2.f), Point_Y - (3.5f *2.f)),
                  FVector2D((float)Point_X - (5.f * (float)2.f), Point_Y - (2.5f *2.f)),
                  FVector2D((float)Point_X - (5.f * (float)2.f), Point_Y - (1.5f *2.f)),
                  FVector2D((float)Point_X - (5.f * (float)2.f), Point_Y - (0.5f *2.f)),
                  FVector2D((float)Point_X - (5.f * (float)2.f), Point_Y + (6.5f *2.f)),
                  FVector2D((float)Point_X - (5.f * (float)2.f), Point_Y + (5.5f *2.f)),
                  FVector2D((float)Point_X - (5.f * (float)2.f), Point_Y + (4.5f *2.f)),

                     FVector2D((float)Point_X - (5.f * (float)2.f), Point_Y + (3.5f *2.f)),
                                   FVector2D((float)Point_X - (5.f * (float)2.f), Point_Y + (2.5f *2.f)),
                                   FVector2D((float)Point_X - (5.f * (float)2.f), Point_Y + (1.5f *2.f)),
                                   FVector2D((float)Point_X - (5.f * (float)2.f), Point_Y),
                                   FVector2D((float)Point_X - (5.f * (float)2.f), Point_Y + (0.2f *2.f)),
                                   FVector2D((float)Point_X - (5.f * (float)2.f), Point_Y - (0.2f *2.f)),
                                   
                                   FVector2D((float)Point_X - (18.5f *2.f), Point_Y - (3.75f *2.f)),
                                   FVector2D((float)Point_X - (18.5f *2.f), Point_Y - (3.f *2.f)),
                                   FVector2D((float)Point_X - (18.5f *2.f), Point_Y - (2.f *2.f)),
                                   FVector2D((float)Point_X - (18.5f *2.f), Point_Y - (1.f *2.f)),
                                   FVector2D((float)Point_X - (18.5f *2.f), Point_Y - (0.5f *2.f)),
                                   FVector2D((float)Point_X - (18.5f *2.f), Point_Y - (0.2f *2.f)),
                                   FVector2D((float)Point_X - (18.5f *2.f), Point_Y),
                                   FVector2D((float)Point_X - (18.5f *2.f), Point_Y + (3.75f *2.f)),
                                   FVector2D((float)Point_X - (18.5f *2.f), Point_Y + (3.f *2.f)),
                                   FVector2D((float)Point_X - (18.5f *2.f), Point_Y + (2.f *2.f)),
                                   FVector2D((float)Point_X - (18.5f *2.f), Point_Y + (1.f *2.f)),
                                   FVector2D((float)Point_X - (18.5f *2.f), Point_Y + (0.5f *2.f)),
                                   FVector2D((float)Point_X - (18.5f *2.f), Point_Y + (0.2f *2.f)),
                     
                                };

                                
                            //      if (gGameplayStatics->ProjectWorldToScreen(localPlayerController, AboveHead, false, &AboveHeadSc)) {
                                        
                                  if (gGameplayStatics->ProjectWorldToScreen(localPlayerController, AboveHead, false,  &AboveHeadSc)) {
                                    
                             // 
                             
                            }else{
                                    
                                    RotateTriangle55(points, angle.Y + 180.0f);
                                    float Thickness = 2.0f;
                                  
                          
                                    DrawArrows(HUD,points, Thickness, colV);
                                    }
                             //     }
                                
                            }
                            
                            
                            */
                    }
                    
                    
                    
                    
                    
                    
                    
                    auto Actors = getActors();
                    for (int i = 0; i < Actors.size(); i++) {
                        auto Actor = Actors[i];
                        if (isObjectInvalid(Actor))
                                continue;
                        
                                /*
                           
                                {
            auto WeaponManagerComponent = localPlayer->WeaponManagerComponent;
              if (WeaponManagerComponent) {
                    auto CurrentWeaponReplicated = (ASTExtraShootWeapon *) WeaponManagerComponent->CurrentWeaponReplicated;//指针指向类名ASTExtraShootWeapon
                    if (CurrentWeaponReplicated) {
                        ZdDq = CurrentWeaponReplicated->CurBulletNumInClip;
                        ZdMax = CurrentWeaponReplicated->CurMaxBulletNumInOneClip;//获取当前枪管中的子弹数量
                        auto wppp = CurrentWeaponReplicated->GetWeaponID();
                       
                        
                        int 子弹命中个数=ZdMax*子追命中率;
                        std::string sb;
                        sb += "当前子弹: ";
                        sb += std::to_string((int)ZdDq);
                        sb += "\n";
                        sb += "子追命中量: ";
                        sb += std::to_string(子弹命中个数);
                     
                       // DrawOutlinedTextFPS(canvas, sb, {(float) glWidth / 2-200, (float) glHeight-300}, COLOR_YELLOW, COLOR_BLACK, true);
                        
                          tslFont->LegacyFontSize = 12.5;
                               DrawText(HUD, sb, {(float) screenWidth / 2-200,(float) screenHeight - 300}, COLOR_YELLOW);//110
                        
                        
                }
              }


int 命中数量 = ZdMax * 子追命中率;

    if (ZdDq > 0) {
        srand((unsigned int)time(NULL));
        int 第n颗子弹 = 1 + rand() % ZdMax; // 随机生成第n颗子弹
        if (第n颗子弹 <= 命中数量) {
            追踪状态 = true;
        } else {
            追踪状态 = false;
            
        }
    } else {
        追踪状态 = false;
        
    }
            }
                                
 if (Actor->IsA(ASTExtraGameStateBase::StaticClass())) {
     
                                auto InGame = (ASTExtraGameStateBase *) Actor;
                                std::string GAME_INFO;

                                GAME_INFO += "Game Info:";
                                GAME_INFO += "\nTeam: ";
                                GAME_INFO += std::to_string((int) InGame->PlayerNumPerTeam);
                                GAME_INFO += "\nTeam Remaining: ";
                                GAME_INFO += std::to_string((int) InGame->AliveTeamNum);
                                GAME_INFO += "\nPlayers Alive: " ;
                                GAME_INFO += std::to_string((int) InGame->AlivePlayerNum);
                                GAME_INFO += "\nReal Players: ";
                                GAME_INFO += std::to_string((int) InGame->PlayerNum);
                                GAME_INFO += "\nTime Match: ";
                                GAME_INFO += std::to_string((int) InGame->ElapsedTime / 60);
                                GAME_INFO += "Minutes";
                                GAME_INFO += "\n\n\n";
                                tslFont->LegacyFontSize = 12;
                               DrawText(HUD, FString(GAME_INFO), {105, (float) screenHeight - 199}, COLOR_WHITE);//110
      
                                }
                                
                                */
                                    
             if (Actors[i]->IsA(APickUpWrapperActor::StaticClass())) {
                        auto PickUp = (APickUpWrapperActor *) Actors[i];
                        if (Config.Material) {
                                int wppp =0;
                                std::string classname = PickUp->GetName();
                                 if (classname.empty());
                                        
                            auto RootComponent = PickUp->RootComponent;
                            if (!RootComponent)
                                continue;

                            float Distance = PickUp->GetDistanceTo(localPlayer) / 100.f;

                               wppp = PickUp->DefineID.TypeSpecificID;
                                FVector Location = RootComponent->RelativeLocation;       
                              FVector2D itemPos;                        
                                if (gGameplayStatics->ProjectWorldToScreen(localPlayerController, Location, false, &itemPos)) { 
                                std::string s;
                                if (Distance <= 100.f) {
    
                                
                                
                             
                                 if (Config.M416) {
                            if (wppp == 101004 ||wppp == 1010041 ||wppp == 1010042 ||wppp == 1010043 ||wppp == 1010044 || wppp == 1010045 || wppp == 1010046 || wppp == 1010047 ) {
                                s += "M416";
                                 s += "[ ";
                                s += std::to_string((int) Distance);
                                s += "M ]";
                                
                            tslFont->LegacyFontSize = fmax(5, 9.5 - (int) (Distance / 100));
                             DrawOutlinedText(HUD,FString(s), FVector2D(itemPos.X, itemPos.Y), {255.f, 248.f, 0.f, 1.f}, COLOR_BLACK, true); //1.f, 0.5f, 0.3f, 1.f
                             tslFont->LegacyFontSize = TSL_FONT_DEFAULT_SIZE;     
                                   }
                            }
    
                                  if (Config.AKM) {
    if (wppp == 101001 ||wppp == 1010011 ||wppp == 1010012 ||wppp == 1010013 ||wppp == 1010014 || wppp == 1010015 || wppp == 1010016 || wppp == 1010017 ) {
                                s += "AKM";
                              s += "[ ";
                                s += std::to_string((int) Distance);
                                s += "M ]";
                                
                            tslFont->LegacyFontSize = fmax(5, 9.5 - (int) (Distance / 100));
                             DrawOutlinedText(HUD,FString(s), FVector2D(itemPos.X, itemPos.Y), {0.9f, 0.2f, 0.6f, 1.f}, COLOR_BLACK, true);
                             tslFont->LegacyFontSize = TSL_FONT_DEFAULT_SIZE;     
                                
        
    }
    }
                            /*
    
        if (wppp == 101002 ||wppp == 1010021 ||wppp == 1010022 ||wppp == 1010023 ||wppp == 1010024 || wppp == 1010025 || wppp == 1010026 || wppp == 1010027 )
    {
                                s += "M16A4";
                              s += "[ ";
                                s += std::to_string((int) Distance);
                                s += "M ]";
                                
                            tslFont->LegacyFontSize = fmax(5, 9.5 - (int) (Distance / 100));
                             DrawOutlinedText(HUD,FString(s), FVector2D(itemPos.X, itemPos.Y), COLOR_RED, COLOR_BLACK, true);
                             tslFont->LegacyFontSize = TSL_FONT_DEFAULT_SIZE;     
                                
       
    }
    */
    
          if (Config.SCARL) {
    if (wppp == 101003 ||wppp == 1010031 ||wppp == 1010032 ||wppp == 1010033 ||wppp == 1010034 || wppp == 1010035 || wppp == 1010036 || wppp == 1010037 )
    {
                                s += "SCAR-L";
                              s += "[ ";
                                s += std::to_string((int) Distance);
                                s += "M ]";
                                
                            tslFont->LegacyFontSize = fmax(5, 9.5 - (int) (Distance / 100));
                             DrawOutlinedText(HUD,FString(s), FVector2D(itemPos.X, itemPos.Y), {1.f, 0.5f, 0.3f, 1.f}, COLOR_BLACK, true);
                             tslFont->LegacyFontSize = TSL_FONT_DEFAULT_SIZE;     
                                
       
    }
    }
    
     if (Config.ACE32) {
        if (wppp == 101102 ||wppp == 1011021 ||wppp == 1011022 ||wppp == 1011023 ||wppp == 1011024 || wppp == 1011025 ||wppp == 1011026|| wppp == 1011027)
    {
                                s += "ACE32";
                              s += "[ ";
                                s += std::to_string((int) Distance);
                                s += "M ]";
                                
                            tslFont->LegacyFontSize = fmax(5, 9.5 - (int) (Distance / 100));
                             DrawOutlinedText(HUD,FString(s), FVector2D(itemPos.X, itemPos.Y), {0.8f, 0.3f, 0.1f, 1.f}, COLOR_BLACK, true);
                             tslFont->LegacyFontSize = TSL_FONT_DEFAULT_SIZE;     
                     
        
    }
    }
    
    
       if (Config.Groza) {

    if (wppp == 101005 ||wppp == 1010051 ||wppp == 1010052 ||wppp == 1010053 ||wppp == 1010054 || wppp == 1010055 || wppp == 1010056 || wppp == 1010057 )  {
   
        
                                s += "Groza";
                              s += "[ ";
                                s += std::to_string((int) Distance);
                                s += "M ]";
                                
                            tslFont->LegacyFontSize = fmax(5, 9.5 - (int) (Distance / 100));
                             DrawOutlinedText(HUD,FString(s), FVector2D(itemPos.X, itemPos.Y), {0.9f, 0.2f, 0.6f, 1.f}, COLOR_BLACK, true);
                             tslFont->LegacyFontSize = TSL_FONT_DEFAULT_SIZE;     
                     
    }}
    
    if (Config.AUG) {

    if (wppp == 101006 ||wppp == 1010061 ||wppp == 1010062 ||wppp == 1010063 ||wppp == 1010064 || wppp == 1010065 || wppp == 10100166 || wppp == 1010067 )
    {
        
                                s += "AUG";
                              s += "[ ";
                                s += std::to_string((int) Distance);
                                s += "M ]";
                                
                            tslFont->LegacyFontSize = fmax(5, 9.5 - (int) (Distance / 100));
                             DrawOutlinedText(HUD,FString(s), FVector2D(itemPos.X, itemPos.Y), {1.f, 0.5f, 0.3f, 1.f}, COLOR_BLACK, true);
                             tslFont->LegacyFontSize = TSL_FONT_DEFAULT_SIZE;     

        
    }
    }
    
    
        if (Config.QBZ) {
        if (wppp == 101007 ||wppp == 1010071 ||wppp == 1010072 ||wppp == 1010073 ||wppp == 1010074 || wppp == 1010075 || wppp == 10100176 || wppp == 1010077 )
    { 
                                s += "QBZ";
                              s += "[ ";
                                s += std::to_string((int) Distance);
                                s += "M ]";
                                
                            tslFont->LegacyFontSize = fmax(5, 9.5 - (int) (Distance / 100));
                             DrawOutlinedText(HUD,FString(s), FVector2D(itemPos.X, itemPos.Y), {1.f, 0.5f, 0.3f, 1.f}, COLOR_BLACK, true);
                             tslFont->LegacyFontSize = TSL_FONT_DEFAULT_SIZE;     

        
    }
    }
    
    
         if (Config.M762) {
    if (wppp == 101008 ||wppp == 1010081 ||wppp == 1010082 ||wppp == 1010083 ||wppp == 1010084 || wppp == 1010085 || wppp == 1010086 || wppp == 1010087 ) {
                           s += "M762";
                              s += "[ ";
                                s += std::to_string((int) Distance);
                                s += "M ]";
                                
                            tslFont->LegacyFontSize = fmax(5, 9.5 - (int) (Distance / 100));
                             DrawOutlinedText(HUD,FString(s), FVector2D(itemPos.X, itemPos.Y), {0.9f, 0.2f, 0.6f, 1.f}, COLOR_BLACK, true);
                             tslFont->LegacyFontSize = TSL_FONT_DEFAULT_SIZE;     

        
    }
    }
    /*
    if (wppp == 101009 ||wppp == 1010091 ||wppp == 1010092 ||wppp == 1010093 ||wppp == 1010094 || wppp == 1010095 || wppp == 1010096 || wppp == 1010097 )
    {
  
              s += "Mk-47";
                              s += "[ ";
                                s += std::to_string((int) Distance);
                                s += "M ]";
                                
                            tslFont->LegacyFontSize = fmax(5, 9.5 - (int) (Distance / 100));
                             DrawOutlinedText(HUD,FString(s), FVector2D(itemPos.X, itemPos.Y), COLOR_RED, COLOR_BLACK, true);
                             tslFont->LegacyFontSize = TSL_FONT_DEFAULT_SIZE;     

    }
    
    */
    
    
     if (Config.G36C) {
    if (wppp == 101010 ||wppp == 1010101 ||wppp == 1010102 ||wppp == 1010103 ||wppp == 1010104 || wppp == 1010105 || wppp == 1010106 || wppp == 1010107 )
    {
  
              s += "G36C";
                              s += "[ ";
                                s += std::to_string((int) Distance);
                                s += "M ]";
                                
                            tslFont->LegacyFontSize = fmax(5, 9.5 - (int) (Distance / 100));
                             DrawOutlinedText(HUD,FString(s), FVector2D(itemPos.X, itemPos.Y), {1.f, 0.5f, 0.3f, 1.f}, COLOR_BLACK, true);
                             tslFont->LegacyFontSize = TSL_FONT_DEFAULT_SIZE;     

        
    }
    }
     if (Config.FAMAS) {
    if (wppp == 101100 ||wppp == 1011001 ||wppp == 1011002 ||wppp == 1011003 ||wppp == 1011004 || wppp == 1011005 || wppp == 1011006 || wppp == 1011007 )
    {
              s += "FAMAS";
                              s += "[ ";
                                s += std::to_string((int) Distance);
                                s += "M ]";
                                
                            tslFont->LegacyFontSize = fmax(5, 9.5 - (int) (Distance / 100));
                             DrawOutlinedText(HUD,FString(s), FVector2D(itemPos.X, itemPos.Y), {1.f, 0.5f, 0.3f, 1.f}, COLOR_BLACK, true);
                             tslFont->LegacyFontSize = TSL_FONT_DEFAULT_SIZE;     

    }
    }
    
      if (Config.M249) {
        if (wppp == 105001 ||wppp == 1050011 ||wppp == 1050012 ||wppp == 1050013 ||wppp == 1050014 || wppp == 1050015 ||wppp == 1050016|| wppp == 1050017)
    {
             s += "M249";
                              s += "[ ";
                                s += std::to_string((int) Distance);
                                s += "M ]";
                                
                            tslFont->LegacyFontSize = fmax(5, 9.5 - (int) (Distance / 100));
                             DrawOutlinedText(HUD,FString(s), FVector2D(itemPos.X, itemPos.Y), {1.f, 0.5f, 0.3f, 1.f}, COLOR_BLACK, true);
                             tslFont->LegacyFontSize = TSL_FONT_DEFAULT_SIZE;     

        
    }
    }
      if (Config.DP28) {
    if (wppp == 105002 ||wppp == 1050021 ||wppp == 1050022 ||wppp == 1050023 ||wppp == 1050024 || wppp == 1050025 ||wppp == 1050026|| wppp == 1050027)
    {

             s += "DP-28";
                              s += "[ ";
                                s += std::to_string((int) Distance);
                                s += "M ]";
                                
                            tslFont->LegacyFontSize = fmax(5, 9.5 - (int) (Distance / 100));
                             DrawOutlinedText(HUD,FString(s), FVector2D(itemPos.X, itemPos.Y), {0.9f, 0.2f, 0.6f, 1.f}, COLOR_BLACK, true);
                             tslFont->LegacyFontSize = TSL_FONT_DEFAULT_SIZE;     

        
    }
    }
                                  
                                   
    
                                   
                                   
                                   
                                                 if (Config.UZI) {
                                
    if (wppp == 102001 ||wppp == 1020011 ||wppp == 1020012 ||wppp == 1020013 ||wppp == 1020014 || wppp == 1020015 ) {//
            s += "UZI";
                              s += "[ ";
                                s += std::to_string((int) Distance);
                                s += "M ]";
                                
                            tslFont->LegacyFontSize = fmax(5, 9.5 - (int) (Distance / 100));
                             DrawOutlinedText(HUD,FString(s), FVector2D(itemPos.X, itemPos.Y), {0.5f, 1.f, 0.8f, 1.f}, COLOR_BLACK, true);
                             tslFont->LegacyFontSize = TSL_FONT_DEFAULT_SIZE;     

  
        
        
    }
    }
    
          if (Config.UMP45) {
                                
    if (wppp == 102002 ||wppp == 1020021 ||wppp == 1020022 ||wppp == 1020023 ||wppp == 1020024 || wppp == 1020025 )
    {//
          s += "UMP-45";
                              s += "[ ";
                                s += std::to_string((int) Distance);
                                s += "M ]";
                                
                            tslFont->LegacyFontSize = fmax(5, 9.5 - (int) (Distance / 100));
                             DrawOutlinedText(HUD,FString(s), FVector2D(itemPos.X, itemPos.Y), {0.2f, 0.8f, 1.f, 1.f}, COLOR_BLACK, true);
                             tslFont->LegacyFontSize = TSL_FONT_DEFAULT_SIZE;     

  
    }
    }
    
      if (Config.Vector) {
    if (wppp == 102003 ||wppp == 1020031 ||wppp == 1020032 ||wppp == 1020033 ||wppp == 1020034 || wppp == 1020035 ||wppp == 1020036 ||wppp == 1020037)
    {//
             s += "Vector";
                              s += "[ ";
                                s += std::to_string((int) Distance);
                                s += "M ]";
                                
                            tslFont->LegacyFontSize = fmax(5, 9.5 - (int) (Distance / 100));
                             DrawOutlinedText(HUD,FString(s), FVector2D(itemPos.X, itemPos.Y), {0.5f, 1.f, 0.8f, 1.f}, COLOR_BLACK, true);
                             tslFont->LegacyFontSize = TSL_FONT_DEFAULT_SIZE;     

  
    }
    }
    
    
    
       if (Config.TommyGun) {
    if (wppp == 102004 ||wppp == 1020041 ||wppp == 1020042 ||wppp == 1020043 ||wppp == 1020044 || wppp == 1020045 ||wppp == 1020046 ||wppp == 1020047)
    {//
         s += "TommyGun";
                              s += "[ ";
                                s += std::to_string((int) Distance);
                                s += "M ]";
                                
                            tslFont->LegacyFontSize = fmax(5, 9.5 - (int) (Distance / 100));
                             DrawOutlinedText(HUD,FString(s), FVector2D(itemPos.X, itemPos.Y), {0.2f, 0.8f, 1.f, 1.f}, COLOR_BLACK, true);
                             tslFont->LegacyFontSize = TSL_FONT_DEFAULT_SIZE;     

  
    }
    }
    
       if (Config.PP19Bizon) {
    if (wppp == 102005 ||wppp == 1020051 ||wppp == 1020052 ||wppp == 1020053 ||wppp == 1020054 || wppp == 1020055 ||wppp == 1020056 ||wppp == 1020057)
    {//
          s += "PP-19 Bizon";
                              s += "[ ";
                                s += std::to_string((int) Distance);
                                s += "M ]";
                                
                            tslFont->LegacyFontSize = fmax(5, 9.5 - (int) (Distance / 100));
                             DrawOutlinedText(HUD,FString(s), FVector2D(itemPos.X, itemPos.Y), {0.5f, 1.f, 0.8f, 1.f}, COLOR_BLACK, true);
                             tslFont->LegacyFontSize = TSL_FONT_DEFAULT_SIZE;     

  
    }
    }
    
    
       if (Config.P90) {
    if (wppp == 102105 ||wppp == 1021051 ||wppp == 1021052 ||wppp == 1021053 ||wppp == 1021054 || wppp == 1021055 ||wppp == 1021056 ||wppp == 1021057)
    {//
       s += "P90";
                              s += "[ ";
                                s += std::to_string((int) Distance);
                                s += "M ]";
                                
                            tslFont->LegacyFontSize = fmax(5, 9.5 - (int) (Distance / 100));
                             DrawOutlinedText(HUD,FString(s), FVector2D(itemPos.X, itemPos.Y), {0.5f, 1.f, 0.8f, 1.f}, COLOR_BLACK, true);
                             tslFont->LegacyFontSize = TSL_FONT_DEFAULT_SIZE;     

  
    }
    }
    
       if (Config.Skorpion) {
        if (wppp == 102007 ||wppp == 1020071 ||wppp == 1020072 ||wppp == 1020073 ||wppp == 1020074 || wppp == 1020075 ||wppp == 1020076 ||wppp == 1020077)
    {//
       s += "Skorpion";
                              s += "[ ";
                                s += std::to_string((int) Distance);
                                s += "M ]";
                                
                            tslFont->LegacyFontSize = fmax(5, 9.5 - (int) (Distance / 100));
                             DrawOutlinedText(HUD,FString(s), FVector2D(itemPos.X, itemPos.Y), {0.5f, 1.f, 0.8f, 1.f}, COLOR_BLACK, true);
                             tslFont->LegacyFontSize = TSL_FONT_DEFAULT_SIZE;     

  
    }
    }
    
    
    
                            
                                     
                                   if (Config.Kar98K) {
    if (wppp == 103001 ||wppp == 1030011 ||wppp == 1030012 ||wppp == 1030013 ||wppp == 1030014 || wppp == 1030015 ||wppp == 1030016 ||wppp == 1030017)
    {
           s += "Kar98K";
                              s += "[ ";
                                s += std::to_string((int) Distance);
                                s += "M ]";
                                
                            tslFont->LegacyFontSize = fmax(5, 9.5 - (int) (Distance / 100));
                             DrawOutlinedText(HUD,FString(s), FVector2D(itemPos.X, itemPos.Y), {0.9f, 0.2f, 0.6f, 1.f}, COLOR_BLACK, true);
                             tslFont->LegacyFontSize = TSL_FONT_DEFAULT_SIZE;     

  
    }
    }
         if (Config.M24) {
    if (wppp == 103002 ||wppp == 1030021 ||wppp == 1030022 ||wppp == 1030023 ||wppp == 1030024 || wppp == 1030025 ||wppp == 1030026 ||wppp == 1030027) {
                   s += "M24";
                              s += "[ ";
                                s += std::to_string((int) Distance);
                                s += "M ]";
                                
                            tslFont->LegacyFontSize = fmax(5, 9.5 - (int) (Distance / 100));
                             DrawOutlinedText(HUD,FString(s), FVector2D(itemPos.X, itemPos.Y), {0.9f, 0.2f, 0.6f, 1.f}, COLOR_BLACK, true);
                             tslFont->LegacyFontSize = TSL_FONT_DEFAULT_SIZE;     

    }
    }
       if (Config.AWM) {
    if (wppp == 103003 ||wppp == 1030031 ||wppp == 1030032 ||wppp == 1030033 ||wppp == 1030034 || wppp == 1030035 ||wppp == 1030036|| wppp == 1030037)
    {
                   s += "AWM";
                              s += "[ ";
                                s += std::to_string((int) Distance);
                                s += "M ]";
                                
                            tslFont->LegacyFontSize = fmax(5, 9.5 - (int) (Distance / 100));
                             DrawOutlinedText(HUD,FString(s), FVector2D(itemPos.X, itemPos.Y), {1.f, 1.f, 0.3f, 1.f}, COLOR_BLACK, true);
                             tslFont->LegacyFontSize = TSL_FONT_DEFAULT_SIZE;     

    }}
     if (Config.AMR) {
       if (wppp == 103012 ||wppp == 1030121 ||wppp == 1030122 ||wppp == 1030123 ||wppp == 1030124 || wppp == 1030125 ||wppp == 1030126|| wppp == 1030127)
    {
   
                           s += "AMR";
                              s += "[ ";
                                s += std::to_string((int) Distance);
                                s += "M ]";
                                
                            tslFont->LegacyFontSize = fmax(5, 9.5 - (int) (Distance / 100));
                             DrawOutlinedText(HUD,FString(s), FVector2D(itemPos.X, itemPos.Y), {1.f, 1.f, 0.3f, 1.f}, COLOR_BLACK, true);
                             tslFont->LegacyFontSize = TSL_FONT_DEFAULT_SIZE;     

        
    }}
                                         
                                     if (Config.mm556) {
                                      
                                     
    
    if (wppp == 303001 ||wppp == 3030011 ||wppp == 3030012 ||wppp == 3030013 ||wppp == 3030014 || wppp == 3030015 ||wppp == 3030016)
    {
           s += "5.56mm";
                              s += "[ ";
                                s += std::to_string((int) Distance);
                                s += "M ]";
                                
                            tslFont->LegacyFontSize = fmax(5, 9.5 - (int) (Distance / 100));
                             DrawOutlinedText(HUD,FString(s), FVector2D(itemPos.X, itemPos.Y), {0, 1.f, 0, 1.f}, COLOR_BLACK, true); //1.f, 0.5f, 0.3f, 1.f
                             tslFont->LegacyFontSize = TSL_FONT_DEFAULT_SIZE;     

  
                             
                       
    }
            }
    if (Config.mm762) {
                                      
    if (wppp == 302001 ||wppp == 3020011 ||wppp == 3020012 ||wppp == 3020013 ||wppp == 3020014 || wppp == 3020015 ||wppp == 3020016)
    {
           s += "7.62mm";
                              s += "[ ";
                                s += std::to_string((int) Distance);
                                s += "M ]";
                                
                            tslFont->LegacyFontSize = fmax(5, 9.5 - (int) (Distance / 100));
                             DrawOutlinedText(HUD,FString(s), FVector2D(itemPos.X, itemPos.Y), {0.9f, 0.2f, 0.6f, 1.f}, COLOR_BLACK, true);
                             tslFont->LegacyFontSize = TSL_FONT_DEFAULT_SIZE;     

  
                             
                       
    }}
                                      
                                       
                                       
                                            
                                         if (Config.mm9) {
                                      
    
    if (wppp == 301001 ||wppp == 3010011 ||wppp == 3010012 ||wppp == 3010013 ||wppp == 3010014 || wppp == 3010015 ||wppp == 3010016)
    {
           s += "9mm";
                              s += "[ ";
                                s += std::to_string((int) Distance);
                                s += "M ]";
                                
                            tslFont->LegacyFontSize = fmax(5, 9.5 - (int) (Distance / 100));
                             DrawOutlinedText(HUD,FString(s), FVector2D(itemPos.X, itemPos.Y), {0.5f, 1.f, 0.8f, 1.f}, COLOR_BLACK, true);
                             tslFont->LegacyFontSize = TSL_FONT_DEFAULT_SIZE;     

  
                             
                        //    .45 ACP
    }
    }
    
        if (Config.ACP45) {
                                      
        if (wppp == 305001 ||wppp == 3050011 ||wppp == 3050012 ||wppp == 3050013 ||wppp == 3050014 || wppp == 3050015 ||wppp == 3050016)
    {
           s += ".45 ACP";
                              s += "[ ";
                                s += std::to_string((int) Distance);
                                s += "M ]";
                                
                            tslFont->LegacyFontSize = fmax(5, 9.5 - (int) (Distance / 100));
                             DrawOutlinedText(HUD,FString(s), FVector2D(itemPos.X, itemPos.Y), {0.2f, 0.8f, 1.f, 1.f}, COLOR_BLACK, true);
                             tslFont->LegacyFontSize = TSL_FONT_DEFAULT_SIZE;     

  
                             
                        //    
    }
                    }
                                      
                                       
                                         if (Config.Bag) {
                             
    
    if (wppp == 501006 ||wppp == 501103 ||wppp == 501104 ||wppp == 501105 ||wppp == 501106)
    {
           s += "Bag";
                              s += "[ ";
                                s += std::to_string((int) Distance);
                                s += "M ]";
                                
                            tslFont->LegacyFontSize = fmax(5, 9.5 - (int) (Distance / 100));
                             DrawOutlinedText(HUD,FString(s), FVector2D(itemPos.X, itemPos.Y), {0.8f, 0.4f, 0.9f, 1.f}, COLOR_BLACK, true);
                             tslFont->LegacyFontSize = TSL_FONT_DEFAULT_SIZE;     
                            
    }                  
                                       
                                       }//Bag(lv,3.4.5.6)        
                                       
                                       
                                       
                                           if (Config.Armor) {
                             
    
    if (wppp == 503003 ||wppp == 503103 ||wppp == 503104 ||wppp == 503105 ||wppp == 503106 ||wppp == 503107 ||wppp == 503108 ||wppp == 503109 ||wppp == 503110 ||wppp == 503111 ||wppp == 503112)
    {
           s += "Armor";
                              s += "[ ";
                                s += std::to_string((int) Distance);
                                s += "M ]";
                                
                            tslFont->LegacyFontSize = fmax(5, 9.5 - (int) (Distance / 100));
                             DrawOutlinedText(HUD,FString(s), FVector2D(itemPos.X, itemPos.Y), {0.8f, 0.4f, 0.9f, 1.f}, COLOR_BLACK, true);
                             tslFont->LegacyFontSize = TSL_FONT_DEFAULT_SIZE;     
                            
    }                  
                                       
                                       }//Armor(lv,3.4.5.6)        
                                       
                            
                                            if (Config.Helmet) {
                             
    
    if (wppp == 502003 ||wppp == 502103 ||wppp == 502104 ||wppp == 502105 ||wppp == 502106 ||wppp == 502107 ||wppp == 502108 ||wppp == 502109 ||wppp == 502110 ||wppp == 502111 ||wppp == 502112)
    {
           s += "Helmet";
                              s += "[ ";
                                s += std::to_string((int) Distance);
                                s += "M ]";
                                
                            tslFont->LegacyFontSize = fmax(5, 9.5 - (int) (Distance / 100));
                             DrawOutlinedText(HUD,FString(s), FVector2D(itemPos.X, itemPos.Y), {0.8f, 0.4f, 0.9f, 1.f}, COLOR_BLACK, true);
                             tslFont->LegacyFontSize = TSL_FONT_DEFAULT_SIZE;     
                            
    }                  
                                       }
                                       }//Armor(lv,3.4.5.6)     
                                       }
                                       
                            }
                        }
                                
                      
                       
                                
                                /*
                                 if (Config.GameInfo)
                            {  
                            if (Actor->IsA(ASTExtraGameStateBase::StaticClass())) {
                                auto InGame = (ASTExtraGameStateBase *) Actor;
                                std::string s;
                     
                                s += "Real Players: ";
                                s += std::to_string((int) InGame->PlayerNum);
                                s += "\nTotal Alive Players: ";
                                s += std::to_string((int) InGame->AlivePlayerNum);
                                s += "\nMatch Time: ";
                                s += std::to_string((int) InGame->ElapsedTime);
                                s += "s";
                               s += "s | ";
                               s += std::to_string((int) InGame->ElapsedTime / 60);
                               s += "m";
                                s += "\n";
                                
       if (!strcmp(GetPackageName().c_str(), "com.tencent.ig")) {
            s += "Global (V5)";
        }
        if (!strcmp(GetPackageName().c_str(), "com.pubg.krmobile")) {
            s += "Korean (V5)";
        }
        if (!strcmp(GetPackageName().c_str(), "com.rekoo.pubgm")) {
            s += "Taiwan (V5)";
        }
        if (!strcmp(GetPackageName().c_str(), "com.vng.pubgmobile")) {
            s += "Vietnam (V5)";
        }
        
                             //   s += GetPackageName().c_str();
                  //              s += "\nFps: ";
                              //  s += std::to_string(m_fps.get());
           
                      //    tslFont->LegacyFontSize = 9.5;
       // DrawOutlinedText2(HUD, FString(s), {(float) SizeX / 8, 480}, COLOR_WHITE, COLOR_BLACK, true);
    //    DrawOutlinedText77(HUD, FString(s), {105, (float) screenHeight - 70}, COLOR_WHITE, true);
        itemfont->LegacyFontSize = 15;
            DrawOutlinedTexttt(HUD, FString(s), {105, (float) screenHeight - 84}, COLOR_WHITE, COLOR_BLACK, true);
          itemfont->LegacyFontSize = 15;
    
        
        
  tslFont->LegacyFontSize = TSL_FONT_DEFAULT_SIZE;

  
  }
  }
                  */
  
        /*
                    if (Actor->IsA(ASTExtraGameStateBase::StaticClass())) {
                            auto GameKero = (ASTExtraGameStateBase*)Actor;
                        
                    
                            if(GameKero->AliveTeamNum == 1){
                                Config.BoxEffect = false;
                                std::string warr;
                         
                               warr += "Chicken Dinner Win (NRG-ENGINE) MOD #1\n";
                                
        if (!strcmp(GetPackageName().c_str(), "com.tencent.ig")) {
            warr += "Global Version";
        }
        if (!strcmp(GetPackageName().c_str(), "com.pubg.krmobile")) {
            warr += "Korean Version";
        }
        if (!strcmp(GetPackageName().c_str(), "com.rekoo.pubgm")) {
            warr += "Taiwan Version";
        }
        if (!strcmp(GetPackageName().c_str(), "com.vng.pubgmobile")) {
            warr += "Vietnam Version";
        }
        
        
            if (Config.AimBot.Enable) {
             if (Config.AimEnable == 0) {
                 warr += "\nAimBot\n";
                  
               warr += std::to_string(int(Config.AimDistance));
               warr += "M";  
              }
            if (Config.AimEnable == 1) {
              warr += "\nBullet Track\n";  
              warr += std::to_string(int(Config.AimDistance));
               warr += "M";  
            }
            }
                    
              if (Config.GameInfo)
                            {  
          
                    tslFont->LegacyFontSize = 19;
                 //   DrawTextIos(HUD, FString(warr), {(float) SizeX / 2, (float) SizeY / 2}, COLOR_RED);
              DrawText2(HUD, FString(warr), {(float) screenWidth / 2, (float) screenHeight / 2}, FLinearColor(255 / 255, 233 / 255, 1 / 255, 255 / 255), true);
        tslFont->LegacyFontSize = TSL_FONT_DEFAULT_SIZE;
            }
}
}
             */

        
                                  if (Config.Grenade)
                            {
                                
                            if (Actor->IsA(ASTExtraGrenadeBase::StaticClass())) {
                                auto Grenade = (ASTExtraGrenadeBase *) Actor;
                                 
                                std::string n = getObjectPath(Grenade);
                                auto RootComponent = Actor->RootComponent;
                                if (!RootComponent)
                                    continue;
                                float Distance = Grenade->GetDistanceTo(localPlayer) / 100.f;
                                
                               
if (Distance <= 10.f) {
    
        if(n=="BP_Grenade_Burn_C.BP_Grenade_Base_C.STExtraGrenadeBase.UAEProjectile.LuaActor.Actor.Object" || n=="BP_Grenade_Shoulei_C.BP_Grenade_Base_C.STExtraGrenadeBase.UAEProjectile.LuaActor.Actor.Object"){
                      std::wstring warr  =L"MOVE, MOVE, MOVE !!!";
                    tslFont->LegacyFontSize = 18;
                    DrawTextIos(HUD, FString(warr), {screenWidth / 2, screenHeight / 2}, COLOR_RED);
                                     
                                    
                                    }
                                    }
    if (Distance <= 110.f) {
                                FVector2D grenadePos;

                                if (W2S(Grenade->K2_GetActorLocation(), &grenadePos)) {
                                    std::string s;
                        
                                           
                                
                                    
                                    if(n=="BP_Grenade_Burn_C.BP_Grenade_Base_C.STExtraGrenadeBase.UAEProjectile.LuaActor.Actor.Object"){
                                //    currentTime = 6;
                                        s+="[ALERT]";
                                           s += " ";
                                    s += std::to_string((int) Distance);
                                    s += "M";
                               
                           
                                    } else if(n=="BP_Grenade_Shoulei_C.BP_Grenade_Base_C.STExtraGrenadeBase.UAEProjectile.LuaActor.Actor.Object"){
                                        s+="[ALERT]";
                                           s += " ";
                                    s += std::to_string((int) Distance);
                                    s += "M";
                        
                                    
                                    }
                                   
                                               tslFont->LegacyFontSize = fmax(5, 10 - (int) (Distance / 100));
                      
                           DrawOutlinedText(HUD,FString(s), FVector2D(grenadePos.X, grenadePos.Y), COLOR_RED, COLOR_BLACK, true);                             
                             tslFont->LegacyFontSize = TSL_FONT_DEFAULT_SIZE;     
                             
                                                  
                                                  
                                                  
                                }
                            }
                        }
                        }
                        
                        
                        
                              if(Config.PlayerESP.LootBox){
                        if (isObjectLootBox(Actors[i])) {
                            APickUpListWrapperActor *PickUpList = (APickUpListWrapperActor *) Actors[i];
                            auto RootComponent = PickUpList->RootComponent;
                            if (!RootComponent)
                              continue;
                                auto PickUpDataList2 = (TArray<FPickUpItemData>)PickUpList->GetDataList(); // auto
                                float Distance = PickUpList->GetDistanceTo(localPlayer) / 100.f;   
                                
                                
                                
                                if (Distance <= 400.f) {
                                    std::string sad = "Box";
                                    sad += "[";
                                    sad += std::to_string((int) Distance);
                                    sad += "M]";
                                    
                                    
                                    
                                    std::string s = "Box";//"BOX";
                                    s += "[";
                                    s += std::to_string((int) Distance);
                                    s += "M]";

                                 FVector2D LootBoxPos;
                                if (W2S(PickUpList->K2_GetActorLocation(), &LootBoxPos)) {
                                      if (!Distance <= 70.f) {
                                 tslFont->LegacyFontSize = fmax(5, 10 - (int) (Distance / 100));
                                    
                                    DrawOutlinedText(HUD,FString(sad), FVector2D(LootBoxPos.X, LootBoxPos.Y), COLOR_THISTLE, COLOR_BLACK, true);
                             tslFont->LegacyFontSize = TSL_FONT_DEFAULT_SIZE;   
                                    }
                                    
                                    
                                     if (Distance <= 70.f) {
                                    tslFont->LegacyFontSize = fmax(5, 10 - (int) (Distance / 100));
                                    
                                    DrawOutlinedText(HUD,FString(s), FVector2D(LootBoxPos.X, LootBoxPos.Y), COLOR_THISTLE, COLOR_BLACK, true);
                             tslFont->LegacyFontSize = TSL_FONT_DEFAULT_SIZE;    
                                     if(Config.PlayerESP.Lootboxitems){
                                        if (Distance <= (float) (17)) {
                                            int i = 0;
                                            for (int j = 0; j < PickUpDataList2.Num(); j++) {
                                                std::vector<std::string> s3;
                                                std::string s2;
                                             //   uint32_t tc;
                                                   for (auto &category : items_data) {
                                                    for (auto &item : category["Items"]) {
                                                        
                           if (item["itemId"] == PickUpDataList2[j].ID.TypeSpecificID) {
                              s2 += item["itemName"].get<std::string>();
                           //   tc = strtoul(item["itemTextColor"].get<std::string>().c_str(), 0, 16);
                              i++;
                              break;
                                }}}
                                
                                              //    if(GetBoxItems(PickUpList->APickUpWrapperActor::GetDataList()[j].ID.TypeSpecificID).length() > 1)   {
                                                
                                                
                           //    s2 = GetBoxItems(PickUpList->GetDataList[j].ID.TypeSpecificID);
                                //  s2 = GetBoxItems(PickUpList->GetDataList()[j].ID.TypeSpecificID)  ;
                            if (PickUpList->GetDataList()[j].Count > 1) {
                               // s2 +=   L" * ";
                                s2 += " * ";
                                s2 += std::to_string(PickUpList->GetDataList()[j].Count);
                            }

                            tslFont->LegacyFontSize = 10.5;//11
                            DrawOutlinedText(HUD,FString(s2), FVector2D(LootBoxPos.X, LootBoxPos.Y- (i * 15.2) ), {0.5f, 1.f, 0.8f, 1.f}, COLOR_BLACK, true);      
                            tslFont->LegacyFontSize = 20;
                                              //  }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        }
                        }
           
                   
                            
                            
                          
                                    if (Config.PlayerESP.Vehicle) {
                        if (isObjectVehicle(Actors[i])) {
                            ASTExtraVehicleBase *Vehicle = (ASTExtraVehicleBase *) Actors[i];
                            auto RootComponent = Vehicle->RootComponent;
                            if (!RootComponent)
                                continue;
                              float Distance = Vehicle->GetDistanceTo(localPlayer) / 100.f;                            
                              FVector Location = RootComponent->RelativeLocation;       
                              FVector2D VehiclePos;                       
                          //    auto VehicleName = USTExtraVehicleUtils::GetVehicleInfo(Vehicle);
                                if (gGameplayStatics->ProjectWorldToScreen(g_LocalController, Location, false, &VehiclePos)) { 
                                         
                                    std::string s = GetVehicleName(Vehicle);
                                    s += " \n[";
                                    s += std::to_string((int) Distance);
                                    s += "M]";
                                    tslFont->LegacyFontSize = fmax(7, 10 - (int) (Distance / 100));

                                    DrawOutlinedText(HUD,FString(s), FVector2D(VehiclePos.X, VehiclePos.Y), {1.f, 1.f, 0, 1.f}, {0, 0, 0, 1.f}, true);            
                                   tslFont->LegacyFontSize = TSL_FONT_DEFAULT_SIZE;
                                    
                                    }                                                                               
                                }
                             }  
                 
                                    
                       //  ASTExtraVehicleBase
                            
                    
                   
                    }
                    
        
                    
  
                    
                            //iAwareText:
                          if(iAwareText){
                             if (totalEnemies > 0) {
                            std::string smm;
         
                   smm += "[ALERT] ";
                          smm += std::to_string((int) totalEnemies);
                             smm += " Enemies in Area";
                             
                            tslFont->LegacyFontSize = 17;
                                 DrawOutlinedText(HUD, FString(smm), FVector2D(screenWidth / 2, 125), COLOR_RED, COLOR_BLACK, true);//23.5
                            tslFont->LegacyFontSize = TSL_FONT_DEFAULT_SIZE; 
                 
                            }
                            /*
                                      if (totalEnemies > 0) {
                                        std::wstring fpd ;
                                   
                                            fpd=L"Enemies: ";
                                        
                                        //fpd=L"ءاﺪﻋﻻﺍ";
                                        fpd +=std::to_wstring((int)totalEnemies);
                                        itemfont->LegacyFontSize = 9 + iAwareTexSiz;//iAwareTexSiz
                                       DrawTextcan(HUD, FString(fpd), FVector2D(screenWidth / 2, 125), visCol, COLOR_BLACK);
                                      //  DrawTextIos(HUD, FString(fpd), {(float) SizeX / 2, 165}, COLOR_WHITE);
                                    
                                    
                                }
                                */
                                
                                
                                
                                
                            }
                        
                            
                }
            }//
            
                    
            
            
            
        }
        
        
    }
    
    {
    
    //IN GAME
    itemfont->LegacyFontSize = 23;//25
            DrawTextcanV2(HUD, "BEAR-ENGINE", FVector2D(screenWidth / 2, 85), COLOR_WHITE, COLOR_BLACK);//90
       itemfont->LegacyFontSize = TSL_FONT_DEFAULT_SIZE;
        
       
       }



                
   if (Config.AimBot.Enable) {
       
         
        if (Config.AimSizeMm == 0) {
      if (!Config.HideFov) {
                DrawCircle33(HUD, screenWidth / 2, screenHeight / 2, Config.AimBot.Cross, 1.0f, COLOR_THISTLE);

        
 }
 
 
 
 }
       }
       
       
    g_LocalPlayer = localPlayer;
    g_LocalController = localPlayerController;
  }
}}}
