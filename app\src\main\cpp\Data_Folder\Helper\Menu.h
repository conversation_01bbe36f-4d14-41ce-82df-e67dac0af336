#pragma once

//ESP* g_pESP = new ESP();
//ESP::ESP() {}
//ESP::~ESP() {}
//
//sf::Vertex line[2];
//sf::VertexArray armsLegs(sf::LinesStrip, 7);
//sf::VertexArray body(sf::LinesStrip, 5);
//sf::CircleShape playerHead(30.f);
//sf::Font font;
//sf::Text playerName;
//sf::RectangleShape healthBar;
//sf::RectangleShape healthBorder;
//sf::RectangleShape infoBg;
//sf::Text playerInfo;
//sf::Text itemName;
//sf::RectangleShape itemBg;

/**
 * @autor SPraditya
 * email help : <EMAIL>
 */

namespace Menu
{
    void ImAim(int x, int y)
    {

    }
    void Render()
    {
        //Move To Main.cpp
    }
}