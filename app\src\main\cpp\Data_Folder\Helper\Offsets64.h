//Global Offset 3.2
#define GNames_Offset 0x7530f0c
#define GEngine_Offset 0xcf07468
#define GetActorArray 0x8f1eb50
#define GNativeAndroidApp 0xc89b248
#define GUObject_Offset 0xcd0fb20

#define UObject_ClassPrivate 0x10
#define UObject_NamePrivate_ComparisonIndex  0x18
#define UClass_SuperStruct  0x30
#define FNameEntry_AnsiName 0xC
#define UEngine_GameViewport 0x810
#define UGameViewportClient_World 0x78
#define UWorld_PersistentLevel  0x30
#define ULevel_Actors 0xA0
#define ACharacter_Mesh 0x488
#define AActor_RootComponent 0x1b0
#define USceneComponent_RelativeLocation  0x184
#define USceneComponent_ComponentVelocity  0x260
#define UCanvas_ViewProjectionMatrix 0x270
#define USceneComponent_Bone  0x860
#define USceneComponent_ComponentToWorld  0x1b0
#define AUAEPlayerController_<PERSON>Key 0x870
#define AUAECharacter_PlayerName 0x8d8
#define AUAECharacter_PlayerKey 0x8f8
#define AUAECharacter_TeamID 0x920
#define AUAECharacter_bIsAI 0x9d1
#define ASTExtraCharacter_Health 0xd98
#define ASTExtraCharacter_HealthMax  0xd9c
#define ASTExtraCharacter_bDead  0xdb4
#define ASTExtraBaseCharacter_WeaponManagerComponent  0x2298
#define ASTExtraBaseCharacter_NearDeatchComponent  0x1910
#define ASTExtraBaseCharacter_NearDeathBreath  0x1930
#define UWeaponManagerComponent_CurrentWeaponReplicated  0x500
#define USTCharacterNearDeathComp_BreathMax 0x16c
#define USTCharacterNearDeathComp_Breath 0x1a4
#define ASTExtraShootWeapon_ShootWeaponComponent 0xec8
#define ASTExtraShootWeaponComponent_ShootWeaponEntityComponent 0x298
#define ASTExtraShootWeapon_ShootWeaponEntityComp 0x1048
#define ASTExtraShootWeapon_ShootWeaponEffectComp 0x1050
#define UShootWeaponEntity_BulletFireSpeed 0x4f8
#define APlayerController_PlayerCameraManager 0x4c0
#define APlayerCameraManager_CameraCacheEntry  0x4a0
#define ASTExtraPlayerController_bIsPressingFireBtn 0x34b0
#define ASTExtraVehicleBase_VehicleShapeType 0x63c
#define AController_ControlRotation 0x458
#define bIsGunAds 0x1029
//#define bIsWeaponFiring 0x1600
#define STExtraGameBase_GameID 0xf68
#define STExtraGameBase_PlayerNumPerTeam 0xe04
#define STExtraGameBase_AlivePlayerNum 0xa2c
#define STExtraGameBase_PlayerNum 0x6e8
#define STExtraGameBase_AliveTeamNum 0xa30
#define STExtraGameBase_ElapsedTime  0x448
#define PickUpWrapperActor_ItemDefineID_TypeSpecificID 0x560 + 0x4
#define STExtraVehicleBase_VehicleShapeType  0x63c
