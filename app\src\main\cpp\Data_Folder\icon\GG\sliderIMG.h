unsigned char sliderHEX[] = {
0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, 0x00, 0x00, 0x00, 0x0D, 0x49, 0x48, 0x44, 0x52, 
0x00, 0x00, 0x00, 0x27, 0x00, 0x00, 0x00, 0x24, 0x08, 0x06, 0x00, 0x00, 0x00, 0x0A, 0x37, 0x23, 
0x9B, 0x00, 0x00, 0x00, 0x09, 0x70, 0x48, 0x59, 0x73, 0x00, 0x00, 0x0B, 0x13, 0x00, 0x00, 0x0B, 
0x13, 0x01, 0x00, 0x9A, 0x9C, 0x18, 0x00, 0x00, 0x00, 0x01, 0x73, 0x52, 0x47, 0x42, 0x00, 0xAE, 
0xCE, 0x1C, 0xE9, 0x00, 0x00, 0x00, 0x04, 0x67, 0x41, 0x4D, 0x41, 0x00, 0x00, 0xB1, 0x8F, 0x0B, 
0xFC, 0x61, 0x05, 0x00, 0x00, 0x01, 0x42, 0x49, 0x44, 0x41, 0x54, 0x78, 0x01, 0xED, 0x98, 0xCB, 
0x4D, 0xC3, 0x40, 0x10, 0x40, 0xDF, 0x2C, 0x42, 0x70, 0x4C, 0x09, 0x2E, 0x21, 0x74, 0x00, 0x1D, 
0x90, 0x0A, 0xA0, 0x04, 0x1F, 0x91, 0x38, 0x60, 0x24, 0x40, 0x48, 0x1C, 0x52, 0x03, 0x1D, 0xD0, 
0x01, 0x29, 0xC1, 0x74, 0x90, 0x12, 0x72, 0xE1, 0x00, 0x87, 0x98, 0x19, 0x27, 0x91, 0x0C, 0x08, 
0x12, 0xC6, 0x28, 0x99, 0xC3, 0xBE, 0xCB, 0xAE, 0xE5, 0x8F, 0x9E, 0x67, 0x67, 0xB5, 0x9A, 0x11, 
0xBE, 0xD0, 0x94, 0x0C, 0x38, 0x64, 0xA8, 0xD3, 0x82, 0x6D, 0x20, 0xCC, 0x68, 0xA8, 0xE5, 0x9E, 
0xE9, 0xF7, 0x5B, 0x5D, 0xB1, 0x4B, 0xAE, 0x74, 0x28, 0xF5, 0xE1, 0x01, 0xDB, 0xA7, 0x66, 0xCE, 
0xA8, 0x2B, 0x29, 0x1D, 0xB1, 0x67, 0x95, 0x3A, 0x66, 0xD7, 0x08, 0xE7, 0x72, 0xCB, 0xE3, 0x62, 
0x4A, 0x2B, 0x36, 0x56, 0xB1, 0x92, 0x38, 0x1C, 0xC9, 0x1D, 0x75, 0x6A, 0x2E, 0x34, 0xB7, 0x62, 
0x89, 0x59, 0xC8, 0xC6, 0x36, 0x24, 0xF6, 0x38, 0x25, 0x1A, 0x9A, 0x5E, 0xB6, 0x31, 0x93, 0x26, 
0xE1, 0x90, 0x88, 0xEC, 0x53, 0x24, 0xA2, 0x72, 0x60, 0x91, 0x0B, 0x4C, 0x96, 0xF3, 0x92, 0xE5, 
0xBC, 0x64, 0x39, 0x2F, 0x59, 0xCE, 0x4B, 0x96, 0xF3, 0x92, 0xE5, 0xBC, 0x64, 0x39, 0x2F, 0xC1, 
0xE5, 0xAC, 0x6E, 0x8C, 0xC8, 0x1B, 0xB3, 0xA4, 0x7A, 0x4F, 0xC4, 0x63, 0x2A, 0x0F, 0x5A, 0x7D, 
0xC9, 0x0D, 0x13, 0xBB, 0x20, 0x12, 0x42, 0x65, 0xC3, 0x2A, 0xE7, 0x46, 0x61, 0x96, 0x57, 0x98, 
0xAC, 0x8A, 0xEA, 0x56, 0xCE, 0x0A, 0x58, 0x2D, 0xC7, 0x4E, 0xD8, 0x75, 0x04, 0x1B, 0x95, 0x7A, 
0xD5, 0x40, 0x2D, 0xF9, 0xDC, 0x2B, 0xA9, 0xB4, 0x47, 0xF2, 0xAE, 0x75, 0xAC, 0x70, 0xB6, 0xE6, 
0x23, 0x05, 0x7F, 0x69, 0xF4, 0x88, 0xFE, 0x3C, 0xBF, 0xAC, 0x4C, 0xC3, 0x8B, 0xE5, 0xFE, 0x32, 
0xC5, 0x3A, 0xAF, 0x39, 0x68, 0xBB, 0x04, 0x49, 0x7B, 0x2B, 0x9B, 0x09, 0x5E, 0xEB, 0xCA, 0x54, 
0x38, 0x70, 0xC9, 0x19, 0x1B, 0x0A, 0xBA, 0xC5, 0x0C, 0xB7, 0x9C, 0xB1, 0x46, 0xB0, 0x97, 0x98, 
0xD1, 0x4B, 0xCE, 0xF8, 0x41, 0xB0, 0xB7, 0x98, 0xD1, 0xFB, 0xF8, 0x6A, 0x9B, 0x7D, 0x73, 0xDD, 
0xE9, 0x8B, 0xA4, 0x37, 0xFE, 0x45, 0x2C, 0x3C, 0x1F, 0xBB, 0x8B, 0x50, 0x45, 0xB4, 0x4B, 0x7D, 
0xF3, 0x00, 0x00, 0x00, 0x00, 0x49, 0x45, 0x4E, 0x44, 0xAE, 0x42, 0x60, 0x82
};