unsigned char subtab1HEX[] = {
0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, 0x00, 0x00, 0x00, 0x0D, 0x49, 0x48, 0x44, 0x52, 
0x00, 0x00, 0x02, 0x11, 0x00, 0x00, 0x00, 0x5D, 0x08, 0x06, 0x00, 0x00, 0x00, 0x1B, 0x9B, 0x34, 
0x2C, 0x00, 0x00, 0x00, 0x09, 0x70, 0x48, 0x59, 0x73, 0x00, 0x00, 0x0B, 0x13, 0x00, 0x00, 0x0B, 
0x13, 0x01, 0x00, 0x9A, 0x9C, 0x18, 0x00, 0x00, 0x00, 0x01, 0x73, 0x52, 0x47, 0x42, 0x00, 0xAE, 
0xCE, 0x1C, 0xE9, 0x00, 0x00, 0x00, 0x04, 0x67, 0x41, 0x4D, 0x41, 0x00, 0x00, 0xB1, 0x8F, 0x0B, 
0xFC, 0x61, 0x05, 0x00, 0x00, 0x02, 0xB1, 0x49, 0x44, 0x41, 0x54, 0x78, 0x01, 0xED, 0xDD, 0xCB, 
0x52, 0xDB, 0x30, 0x00, 0x86, 0x51, 0x29, 0x97, 0xB6, 0x6C, 0xE0, 0xFD, 0x1F, 0x13, 0x36, 0xDC, 
0x92, 0xB8, 0x52, 0xAA, 0x80, 0xC3, 0x40, 0x1A, 0xFF, 0x3B, 0x86, 0x73, 0x66, 0x84, 0x26, 0x24, 
0xDE, 0x7F, 0x23, 0xC9, 0x76, 0x2D, 0x17, 0x4C, 0xD3, 0xB4, 0x6A, 0xD3, 0xAF, 0x36, 0xD6, 0x6D, 
0xD4, 0x31, 0xAF, 0x67, 0x3F, 0x59, 0x15, 0x00, 0xE0, 0x3B, 0x3B, 0xCC, 0xE6, 0x69, 0x8C, 0x7D, 
0x1B, 0xAF, 0x6D, 0xEC, 0x6A, 0xAD, 0x87, 0xAF, 0x2E, 0xAC, 0x9F, 0xFD, 0xB3, 0xC5, 0xC3, 0xEF, 
0x36, 0xFD, 0x69, 0x63, 0x5B, 0x00, 0x80, 0x9F, 0x6C, 0xD7, 0xC6, 0x63, 0x8B, 0x89, 0xE7, 0x8F, 
0x5F, 0x9C, 0x45, 0xC4, 0x58, 0x79, 0xB8, 0x6D, 0x63, 0x53, 0x00, 0x00, 0xDE, 0xF5, 0xD5, 0x89, 
0xFB, 0xF9, 0xCA, 0xC4, 0x5B, 0x44, 0x8C, 0x80, 0xB8, 0x2B, 0xE7, 0xDB, 0x15, 0x00, 0x00, 0x27, 
0x67, 0x21, 0x31, 0x3F, 0xD3, 0x20, 0x20, 0x00, 0x80, 0x4B, 0x7A, 0x27, 0xDC, 0x9E, 0x3E, 0x1C, 
0x23, 0x62, 0x9C, 0x81, 0x10, 0x10, 0x00, 0xC0, 0xFF, 0x6C, 0x46, 0x37, 0xBC, 0xAD, 0x44, 0xDC, 
0x14, 0x00, 0x80, 0xEB, 0xF4, 0x9B, 0x2F, 0xCA, 0x6A, 0x9C, 0x85, 0x70, 0x90, 0x12, 0x00, 0xB8, 
0xD6, 0xB6, 0xF7, 0x83, 0x80, 0x00, 0x00, 0x12, 0xDB, 0x1E, 0x11, 0x9E, 0x05, 0x01, 0x00, 0x2C, 
0xB5, 0xE9, 0x11, 0xE1, 0xA9, 0x93, 0x00, 0xC0, 0x52, 0x55, 0x44, 0x00, 0x00, 0x89, 0x75, 0x0F, 
0x88, 0x5A, 0x00, 0x00, 0x96, 0x59, 0x59, 0x89, 0x00, 0x00, 0x22, 0x02, 0x02, 0x00, 0x88, 0x58, 
0x89, 0x00, 0x00, 0x12, 0x6B, 0x01, 0x01, 0x00, 0x44, 0x44, 0x04, 0x00, 0x10, 0x11, 0x11, 0x00, 
0x40, 0x44, 0x44, 0x00, 0x00, 0x11, 0x11, 0x01, 0x00, 0x44, 0x44, 0x04, 0x00, 0x10, 0x11, 0x11, 
0x00, 0x40, 0x44, 0x44, 0x00, 0x00, 0x11, 0x11, 0x01, 0x00, 0x44, 0x44, 0x04, 0x00, 0x10, 0x11, 
0x11, 0x00, 0x40, 0x44, 0x44, 0x00, 0x00, 0x11, 0x11, 0x01, 0x00, 0x44, 0x44, 0x04, 0x00, 0x10, 
0x11, 0x11, 0x00, 0x40, 0x44, 0x44, 0x00, 0x00, 0x11, 0x11, 0x01, 0x00, 0x44, 0x44, 0x04, 0x00, 
0x10, 0x11, 0x11, 0x00, 0x40, 0x44, 0x44, 0x00, 0x00, 0x11, 0x11, 0x01, 0x00, 0x44, 0x44, 0x04, 
0x00, 0x10, 0x11, 0x11, 0x00, 0x40, 0x44, 0x44, 0x00, 0x00, 0x11, 0x11, 0x01, 0x00, 0x44, 0x44, 
0x04, 0x00, 0x10, 0x11, 0x11, 0x00, 0x40, 0x44, 0x44, 0x00, 0x00, 0x11, 0x11, 0x01, 0x00, 0x44, 
0x44, 0x04, 0x00, 0x10, 0x11, 0x11, 0x00, 0x40, 0x44, 0x44, 0x00, 0x00, 0x11, 0x11, 0x01, 0x00, 
0x44, 0x44, 0x04, 0x00, 0x10, 0x11, 0x11, 0x00, 0x40, 0x44, 0x44, 0x00, 0x00, 0x11, 0x11, 0x01, 
0x00, 0x44, 0x44, 0x04, 0x00, 0x10, 0x11, 0x11, 0x00, 0x40, 0x44, 0x44, 0x00, 0x00, 0x11, 0x11, 
0x01, 0x00, 0x44, 0x44, 0x04, 0x00, 0x10, 0x11, 0x11, 0x00, 0x40, 0x44, 0x44, 0x00, 0x00, 0x11, 
0x11, 0x01, 0x00, 0x44, 0x44, 0x04, 0x00, 0x10, 0x11, 0x11, 0x00, 0x40, 0x44, 0x44, 0x00, 0x00, 
0x11, 0x11, 0x01, 0x00, 0x44, 0x44, 0x04, 0x00, 0x10, 0x11, 0x11, 0x00, 0x40, 0x44, 0x44, 0x00, 
0x00, 0x11, 0x11, 0x01, 0x00, 0x44, 0x44, 0x04, 0x00, 0x10, 0x11, 0x11, 0x00, 0x40, 0x44, 0x44, 
0x00, 0x00, 0x11, 0x11, 0x01, 0x00, 0x44, 0x44, 0x04, 0x00, 0x10, 0x11, 0x11, 0x00, 0x40, 0x44, 
0x44, 0x00, 0x00, 0x11, 0x11, 0x01, 0x00, 0x44, 0x44, 0x04, 0x00, 0x10, 0xE9, 0x11, 0x71, 0x28, 
0x00, 0x00, 0xCB, 0x4C, 0x56, 0x22, 0x00, 0x80, 0xC4, 0x41, 0x44, 0x00, 0x00, 0x89, 0xE3, 0x4A, 
0xC4, 0xBE, 0x00, 0x00, 0x2C, 0x23, 0x22, 0x00, 0x80, 0xC8, 0x31, 0x22, 0xA6, 0x02, 0x00, 0xB0, 
0xCC, 0xDE, 0x4A, 0x04, 0x00, 0x90, 0x78, 0xED, 0x11, 0xF1, 0x52, 0x00, 0x00, 0x96, 0xD9, 0xAD, 
0x6A, 0xAD, 0xFD, 0x39, 0x11, 0xAF, 0x05, 0x00, 0xE0, 0x3A, 0xBB, 0xDE, 0x0F, 0xA7, 0x5B, 0x3C, 
0x9F, 0x0A, 0x00, 0xC0, 0x75, 0x1E, 0xFB, 0x9F, 0x63, 0x44, 0xB4, 0x9A, 0x78, 0x6E, 0xD3, 0xAE, 
0x00, 0x00, 0x5C, 0xB6, 0x1F, 0xDD, 0x70, 0xF6, 0xEE, 0x8C, 0x87, 0xE2, 0x90, 0x25, 0x00, 0xF0, 
0xB5, 0xDE, 0x09, 0xF7, 0xA7, 0x0F, 0x75, 0xFE, 0xCD, 0x34, 0x1D, 0x6F, 0xF9, 0xBC, 0x6B, 0x63, 
0x5D, 0x00, 0x00, 0xDE, 0xF5, 0x1D, 0x8B, 0x87, 0x71, 0x96, 0xF2, 0xA8, 0x7E, 0xF6, 0xAB, 0x16, 
0x13, 0xBF, 0xDB, 0x74, 0xD3, 0xC6, 0xA6, 0x00, 0x00, 0x3F, 0x59, 0xBF, 0xF9, 0xE2, 0xE9, 0xB4, 
0x85, 0x31, 0x57, 0x2F, 0x5D, 0x35, 0x56, 0x26, 0x7A, 0x48, 0x6C, 0xCB, 0xBF, 0xAD, 0x8F, 0xD5, 
0xB8, 0xE6, 0xB4, 0x0D, 0xE2, 0xDD, 0x1B, 0x00, 0xF0, 0xBD, 0xCD, 0xDF, 0xE6, 0xBD, 0x1F, 0x63, 
0x1A, 0xF3, 0xCB, 0x7C, 0xE5, 0xE1, 0xA3, 0xBF, 0x48, 0x2D, 0x5B, 0xB4, 0x7E, 0x8E, 0xEA, 0x12, 
0x00, 0x00, 0x00, 0x00, 0x49, 0x45, 0x4E, 0x44, 0xAE, 0x42, 0x60, 0x82
};