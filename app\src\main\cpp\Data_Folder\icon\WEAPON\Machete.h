unsigned char Machete_data[] = {
  0x89, 0x50, 0x4e, 0x47, 0x0d, 0x0a, 0x1a, 0x0a, 00, 00, 00, 0x0d, 0x49, 0x48, 0x44, 0x52,
  00, 00, 00, 0x64, 00, 00, 00, 0x2b, 0x08, 0x06, 00, 00, 00, 0x8e, 0xec, 0x2d,
  0xed, 00, 00, 00, 0x01, 0x73, 0x52, 0x47, 0x42, 00, 0xae, 0xce, 0x1c, 0xe9, 00, 00,
  00, 0x04, 0x73, 0x42, 0x49, 0x54, 0x08, 0x08, 0x08, 0x08, 0x7c, 0x08, 0x64, 0x88, 00, 00,
  0x0a, 0x01, 0x49, 0x44, 0x41, 0x54, 0x78, 0x9c, 0xed, 0x5b, 0x7b, 0x70, 0x54, 0xd5, 0x19, 0xff,
  0xdd, 0x7d, 0x65, 0x43, 0x9e, 0x4b, 0x80, 00, 0x25, 0x45, 0x20, 0x24, 0x30, 0x22, 0x28, 0x28,
  0x95, 0x87, 0xd6, 0x8e, 0x60, 0x99, 0x16, 0x6b, 0xb0, 0x16, 0x9d, 0x3a, 0x4a, 0x65, 0x32, 0x93,
  0xa9, 0xcf, 0x29, 0xb5, 0x38, 0x53, 0x07, 0xc8, 0x8c, 0x63, 0xeb, 0x8c, 0x4e, 0xc0, 0xd6, 0xc7,
  0xf8, 0x68, 0xa7, 0x11, 0xd4, 0x71, 0x5a, 0x71, 0x04, 0xb4, 0xc5, 0x1a, 0x10, 0xc2, 0x58, 0x08,
  0xe0, 0x04, 0x08, 0x58, 0x10, 0x92, 0x66, 0x78, 0xe4, 0x41, 0x12, 0x36, 0xbb, 0xd9, 0xc7, 0x3d,
  0xf7, 0x7c, 0xdf, 0xe9, 0x1f, 0xe7, 0xde, 0xec, 0x66, 0x81, 0x08, 0x1a, 0x50, 0xc9, 0xfe, 0x32,
  0x77, 0xf6, 0xdc, 0xbb, 0x67, 0x77, 0xcf, 0x7e, 0xdf, 0x7e, 0xbf, 0xf3, 0xfb, 0xbe, 0xef, 0x06,
  0x48, 0x23, 0x8d, 0x34, 0xd2, 0xf8, 0xce, 0xc0, 0x9d, 0x72, 0xee, 0x05, 0x90, 0x0f, 0x20, 0x0b,
  0x80, 0x27, 0xe9, 0xba, 0xba, 0x6c, 0x2b, 0x1a, 0xe4, 0x30, 0x92, 0xc6, 0xbe, 0x40, 0x20, 0x30,
  0xa9, 0xb1, 0xb1, 0x71, 0x93, 0x14, 0x32, 0xde, 0xda, 0xde, 0xfa, 0xbf, 0xba, 0xba, 0xba, 0xdd,
  0x6f, 0xbc, 0xf1, 0xc6, 0xce, 0x6d, 0xdb, 0xb6, 0x35, 0x03, 0xe8, 0x04, 0x10, 0x02, 0x10, 0x07,
  0x20, 0x91, 0x76, 0xd2, 0x25, 0x85, 0x01, 0x60, 0x84, 0xcf, 0xe7, 0xbb, 0x2b, 0x12, 0x8d, 0xc4,
  0x2c, 0xcb, 0x12, 0xcc, 0xcc, 0x92, 0x48, 0x30, 0x33, 0x13, 0x91, 0xd5, 0xd5, 0xd9, 0x75, 0xa2,
  0xae, 0xae, 0xee, 0x1f, 0xcf, 0x3d, 0xf7, 0xdc, 0x63, 0x73, 0xe6, 0xcc, 0xb9, 0x09, 0xc0, 0x58,
  00, 0x79, 0xd0, 0x51, 0x95, 0xc6, 00, 0xc3, 00, 0x30, 0x1a, 0xc0, 0xbd, 0xb1, 0x48, 0x2c,
  0xc6, 0xc4, 0x8a, 0x59, 0x29, 0x56, 0x1a, 0xcc, 0xf6, 0x39, 0xb3, 0x62, 0x66, 0x96, 0x52, 0x9a,
  0x3d, 0x3d, 0x91, 0x8e, 0x86, 0x86, 0x86, 0x7f, 0x3e, 0xb6, 0xec, 0xb1, 0x0a, 0x9f, 0xcf, 0x37,
  0x15, 0xc0, 0x48, 00, 0x43, 0x70, 0x36, 0x0d, 0xa6, 0xf1, 0x15, 0x51, 0x08, 0xe0, 0x17, 0x6d,
  0x6d, 0x6d, 0x9d, 0x4a, 0x69, 0x67, 0x38, 0x0e, 0x51, 0x7d, 0xc7, 0x2c, 0x6d, 0x30, 0xf7, 0x9e,
  0x5b, 0xa7, 0x4e, 0x9d, 0xfa, 0x7c, 0xfd, 0xfa, 0xf5, 0xcf, 0x97, 0x97, 0x97, 0xdf, 0x01, 0xa0,
  0x04, 0xc0, 0x50, 00, 0x19, 0xe8, 0x4b, 0x8b, 0x69, 0x5c, 0x04, 0x0a, 00, 0xfc, 0xac, 0xf1,
  0x58, 0xe3, 0xf1, 0xf3, 0x38, 0x22, 0x25, 0x62, 0xd8, 0xb9, 0xc8, 0x4c, 0xbd, 0xb3, 0x98, 0x98,
  0x28, 0x1e, 0x8f, 0x87, 0x9b, 0x9a, 0x9a, 0xea, 0x9e, 0xad, 0x7a, 0xf6, 0x77, 0xb9, 0xb9, 0xb9,
  0x3f, 00, 0x30, 0x06, 0x40, 0x36, 0xd2, 0xd1, 0x73, 0x51, 0xc8, 0x07, 0xb0, 0x60, 0x57, 0xdd,
  0xae, 0x83, 0xac, 0x94, 0x22, 0x22, 0xee, 0x35, 0xba, 0x4a, 0xf1, 0x0a, 0xab, 0x73, 0x3c, 0x91,
  0x32, 0x4d, 0x43, 0x31, 0xb3, 0x0c, 0x76, 0x05, 0x8f, 0x7f, 0xfc, 0xf1, 0xc7, 0x7f, 0x7b, 0xfc,
  0x37, 0x8f, 0xdf, 0x53, 0x50, 0x50, 0x30, 0x09, 0xda, 0xf9, 0x7e, 0xa4, 0xa3, 0xa7, 0x5f, 0xe4,
  00, 0xf8, 0xd1, 0xa6, 0x4d, 0x9b, 0x3e, 0x55, 0xb6, 0x43, 0x88, 0x88, 0x94, 0xb3, 0x77, 0x24,
  0x39, 0x84, 0x89, 0x95, 0x13, 0x15, 0x5f, 0x16, 0x4d, 0xc9, 0x63, 0x92, 0x24, 0x3a, 0x3a, 0x3b,
  0x5a, 0x4e, 0x9e, 0x38, 0xb9, 0xf7, 0xb5, 0xbf, 0xbc, 0x56, 0x59, 0x54, 0x54, 0x74, 0x13, 0x80,
  0xef, 0x43, 0x8b, 0x03, 0x4f, 0xff, 0xcb, 0x1b, 0x7c, 0x18, 0x02, 0x60, 0xee, 0xab, 0xaf, 0xbd,
  0xfa, 0x01, 0x2b, 0xa5, 0x2c, 0xcb, 0xb2, 0xa4, 0x94, 0xcc, 0xac, 0x58, 0x4a, 0x49, 0x0e, 0x55,
  0x29, 0x56, 0xac, 0x48, 0x31, 0x13, 0x27, 0xae, 0xf7, 0x09, 0x8c, 0x24, 0x6a, 0x4b, 0x75, 0x0c,
  0x33, 0x07, 0x83, 0xc1, 0x33, 0x8a, 0x15, 0x49, 0x96, 0x4c, 0x44, 0x1c, 0x0a, 0x85, 0xda, 0xeb,
  0xf6, 0xd4, 0xad, 0xaf, 0xac, 0xac, 0x2c, 0x2f, 0x2e, 0x2e, 0x9e, 0x8e, 0x84, 0x38, 0x70, 0x7d,
  0xb3, 0xe6, 0xf8, 0xe6, 0xe1, 0x07, 0x70, 0xc3, 0xca, 0xca, 0x95, 0xeb, 0x7a, 0xb9, 0x8a, 0x15,
  0x13, 0x11, 0x0b, 0x21, 0xac, 0xe4, 0xe8, 0x50, 0xb6, 0xd1, 0x89, 0x88, 0x52, 0xe8, 0x2c, 0xd5,
  0x07, 0xe7, 0x84, 0x1d, 0x79, 0xdc, 0x1b, 0x7d, 0xfa, 0xe3, 0x58, 0x0a, 0x8a, 0xb7, 0xb5, 0xb5,
  0x35, 0x6e, 0xfa, 0x70, 0xd3, 0x9f, 0x67, 0xcf, 0x9e, 0x7d, 0x5b, 0x46, 0x46, 0x46, 0x31, 0x80,
  00, 00, 0x1f, 0x06, 0x21, 0xbd, 0xf9, 00, 0x4c, 0x5d, 0xbc, 0x78, 0x71, 0x95, 0xb0, 0x84,
  0x64, 0xa5, 0x14, 0xb3, 0x52, 0x52, 0xea, 0x5f, 0x32, 0x3b, 0xa6, 0x66, 0xa5, 0x98, 0x12, 0x66,
  0xbf, 0x18, 0xca, 0x52, 0xf6, 0x7b, 0x3a, 0x0e, 0x3f, 0x9f, 0x8a, 0x33, 0x4d, 0x33, 0xca, 0xc4,
  0x1c, 0x89, 0x44, 0x82, 0x07, 0x0f, 0x1e, 0xfc, 0xa8, 0x6a, 0x4d, 0xd5, 0x6f, 0x67, 0xce, 0x9c,
  0x39, 0x0b, 0xc0, 0xf7, 0xa0, 0xc5, 0x81, 0x07, 0x83, 0xc0, 0x41, 0x1e, 00, 0x25, 0xb7, 0xdc,
  0x7c, 0xcb, 0xaa, 0xee, 0xee, 0xee, 0x50, 0x1f, 0x1b, 0x12, 0x2b, 0x45, 0x89, 0xbd, 0x83, 0x75,
  0x5c, 0xf0, 0xfb, 0x1b, 0xde, 0xdf, 0x5d, 0x5e, 0x5e, 0xfe, 0xf7, 0xdd, 0x7b, 0x76, 0x1f, 0x4d,
  0x36, 0x72, 0x1f, 0xca, 0xa2, 0x84, 0x22, 0x23, 0x22, 0x36, 0x4d, 0xd3, 0xb4, 0xa3, 0xac, 0xd7,
  0x07, 0xc4, 0xd4, 0x2b, 0x22, 0x22, 0x91, 0x48, 0x64, 0xff, 0x81, 0xfd, 0x8d, 0x52, 0x4a, 0x2b,
  0x49, 0x18, 0x28, 0x29, 0xa5, 0x19, 0x0a, 0x85, 0x5a, 0xb6, 0x6f, 0xdf, 0xbe, 0x76, 0xe1, 0x1d,
  0x0b, 0x17, 0x65, 0x64, 0x64, 0x94, 0x40, 0x0b, 0x91, 0x2b, 0x36, 0x31, 0x75, 0x03, 0x18, 0x7f,
  0xf5, 0x35, 0x57, 0x2f, 0x4f, 0xa5, 0x22, 0x3b, 0x5b, 0x67, 0xc5, 0x8a, 0x2d, 0xcb, 0x92, 0x8e,
  0x29, 0x85, 0x10, 0x66, 0xed, 0x8e, 0xda, 0x03, 0xcb, 0x97, 0x2f, 0xff, 0x80, 0x88, 0xe4, 0xb9,
  0xe8, 0x49, 0x67, 0xfc, 0x52, 0x32, 0x33, 0x31, 0x33, 0x09, 0x21, 0x04, 0x33, 0x93, 0x94, 0xb2,
  0x77, 0xbe, 0x94, 0x52, 0x12, 0x91, 0x54, 0xac, 0x94, 0x65, 0x59, 0xc2, 0x34, 0x4d, 0x61, 0xbf,
  0xf4, 0x7c, 0x7a, 0x8e, 0x63, 0x3d, 0xb1, 0xee, 0x7d, 0xfb, 0xf6, 0xfd, 0xeb, 0xe9, 0x67, 0x9e,
  0x7e, 0x68, 0xec, 0xd8, 0xb1, 0xce, 0xde, 0x93, 0x89, 0x2b, 0x68, 0xef, 0x71, 0x01, 0xb8, 0x2a,
  0x37, 0x37, 0xf7, 0x61, 0x29, 0xa5, 0xc5, 0x8e, 0x45, 0x98, 0xa5, 0xde, 0xdf, 0xa5, 0x75, 0xc1,
  0x34, 0xc5, 0x7d, 0xaf, 0xc7, 0x62, 0x31, 0xf3, 0xc8, 0x91, 0x23, 0x27, 0x99, 0xd9, 0x51, 0x6f,
  0x2c, 0x93, 0x68, 0x90, 0x99, 0x89, 0xa4, 0x56, 0x74, 0xf6, 0xf3, 0x92, 0x15, 0x73, 0x34, 0x1a,
  0x8d, 0x73, 0x8a, 0xf8, 0x4e, 0x11, 0x09, 0x8a, 0x99, 0x49, 0x58, 0xc2, 0xec, 0x38, 0xdd, 0x71,
  0x74, 0x6d, 0xf5, 0xda, 0x67, 0xc6, 0x97, 0x8c, 0x9f, 0x0b, 0x9d, 0xf7, 0x64, 0xe1, 0x0a, 0xc8,
  0x7b, 0xc6, 0xb8, 0xe1, 0x7e, 0xc0, 0x34, 0x4d, 0x93, 0x99, 0x39, 0x1c, 0x0e, 0x47, 0x84, 0x10,
  0x16, 0x11, 0xa9, 0xe4, 0x83, 0x1d, 0x2e, 0x49, 0x30, 0x54, 0xb2, 0x2f, 0x14, 0x27, 0x39, 0xce,
  0x51, 0x6c, 0x5b, 0xb7, 0x6e, 0x6d, 0xd0, 0x81, 0x66, 0x73, 0x53, 0xea, 0x9f, 0x0d, 0x22, 0xe2,
  0x58, 0x2c, 0x16, 0x73, 0xa2, 0xcb, 0x9e, 0x7f, 0x21, 0xfb, 0x13, 0x33, 0xb3, 0x14, 0xd2, 0x12,
  0x5d, 0x5d, 0x5d, 0x27, 0xb6, 0x6c, 0xd9, 0x52, 0xfd, 0xe0, 0x83, 0x0f, 0xde, 0x0d, 0x60, 0x12,
  0xbe, 0x43, 0x55, 0x83, 0xd4, 0x05, 0x0e, 0x07, 0x70, 0xc3, 0xa1, 0x43, 0x87, 0xfe, 0xd8, 0xde,
  0xde, 0x1e, 0x39, 0x7e, 0xf2, 0xf8, 0x99, 0xae, 0xf6, 0xae, 0x9e, 0x70, 0x24, 0x6c, 0x6a, 0x56,
  0x21, 0xf6, 0x65, 0xf8, 0xdc, 0x2e, 0x8f, 0xcb, 0x9b, 0x9d, 0x91, 0xed, 0xc9, 0xca, 0xc9, 0xca,
  0xcc, 0xcb, 0xcd, 0xcb, 0x1a, 0x3e, 0x62, 0x78, 0x6e, 0x20, 0x10, 0xc8, 0x2b, 0x2c, 0x2c, 0xcc,
  0xcb, 0xc9, 0xce, 0xc9, 0xce, 0xf0, 0x67, 0x78, 0xa1, 0xe0, 0x72, 0xbb, 0xdc, 0x06, 0x5c, 0x30,
  0x94, 0x52, 0x06, 0x74, 0x75, 0xd8, 0x30, 0x0c, 0xfd, 0x91, 0x0a, 00, 0x94, 0x82, 0x01, 0x03,
  0xca, 00, 0x94, 0x62, 0xe5, 0x32, 0x5c, 0x50, 0x80, 0xe1, 0x4c, 0x0e, 0x9e, 0x39, 0x13, 0xf6,
  0xfb, 0xfd, 0x3e, 0xbf, 0x3f, 0x33, 0x03, 0x46, 0x62, 0xb1, 0x0a, 0xe7, 0x19, 0x2b, 0xa5, 0x14,
  0x94, 0x32, 0x60, 0xb8, 0xa0, 0x3f, 0x8c, 0x84, 0x10, 0xa2, 0xb9, 0xb9, 0x79, 0xff, 0xdb, 0xeb,
  0xde, 0x7e, 0x73, 0xf5, 0xcb, 0xab, 0x77, 0x04, 0x4f, 0x07, 0x4f, 0x41, 0x57, 0xad, 0x4d, 00,
  0x7c, 0x49, 0xac, 0xfa, 0x35, 0x90, 0xea, 0x90, 0x4c, 0xe8, 0x2c, 0x3a, 0xd3, 0x7e, 0xce, 0x82,
  0x5e, 0xb4, 0x42, 0xa2, 0xdc, 0x6e, 0x40, 0xd3, 0x9b, 0x01, 0x4d, 0x09, 0x1e, 0xe8, 0x8d, 0xd5,
  0x6f, 0x1f, 0x59, 00, 0x72, 0x46, 0x8d, 0x1a, 0x35, 0x74, 0xfa, 0xf4, 0xe9, 0xa3, 0x67, 0xcd,
  0x9a, 0x35, 0xee, 0xc6, 0x99, 0x37, 0x96, 0x96, 0x4c, 0x2a, 0x29, 0x1a, 0x39, 0x72, 0xe4, 0x30,
  0xaf, 0xd7, 0xdb, 0xab, 0x90, 0x52, 0x8d, 0xe9, 0x78, 0xcb, 0xbe, 0xae, 0x84, 0x10, 0x96, 0xcf,
  0xe7, 0xf3, 0xda, 0x17, 0xce, 0x7e, 0xcd, 0xc5, 0x8c, 0x19, 0x90, 0x4a, 0x5a, 0x6d, 0xad, 0x6d,
  0x87, 0xf7, 0xee, 0xde, 0xbb, 0xf9, 0xf5, 0xbf, 0xbe, 0x5e, 0xb3, 0x71, 0xe3, 0xc6, 0xcf, 0x01,
  0x74, 0x01, 0x88, 0x42, 0xb7, 0x14, 0xbe, 0x71, 0x9c, 0x2b, 0x84, 0x93, 0xbf, 0xcb, 0x85, 0xbe,
  0xbe, 0x3f, 0x27, 0x65, 0x43, 0x67, 0xe2, 0xc3, 0xfc, 0x7e, 0xff, 0x88, 0x89, 0xa5, 0x13, 0xbf,
  0xbf, 0xe4, 0xde, 0x25, 0x53, 0x16, 0x95, 0x2d, 0x9a, 0x5d, 0x34, 0xb6, 0xa8, 0xd0, 0xed, 0x71,
  0x7b, 0x5d, 0x86, 0xab, 0xd7, 0xe0, 0xa9, 0x60, 0x66, 0x06, 0xa0, 0xa4, 0x94, 0xec, 0x76, 0xb9,
  0x0d, 0x97, 0xdb, 0xe5, 0x46, 0x52, 0xa4, 0x5d, 0x2c, 0x94, 0x52, 0xce, 0x23, 0x5b, 0x71, 0x19,
  0xfe, 0x6c, 0xdf, 0xde, 0xad, 0x55, 0x55, 0x55, 0x6f, 0x6d, 0xd8, 0xb0, 0xe1, 0x80, 0x10, 0xe2,
  0x34, 0x80, 0x1e, 00, 0xe2, 0x02, 0xbf, 0xff, 0x80, 0xe3, 0x52, 0x73, 0xaa, 0xf3, 0xfe, 0xc9,
  0x0e, 0xca, 0x82, 0xed, 0x20, 00, 0xc3, 0xae, 0xbf, 0xfe, 0xfa, 0x71, 0x77, 0x96, 0xdd, 0x79,
  0xcd, 0x6d, 0x0b, 0x6e, 0xbb, 0xb6, 0x78, 0x7c, 0xf1, 0x55, 0xb9, 0x79, 0xb9, 0x43, 0x0c, 0x97,
  0xe1, 0x56, 0x4a, 0x29, 0x18, 0x86, 0xa1, 0x98, 0xd9, 0x30, 0x0c, 0x05, 0x05, 0xb4, 0x77, 0xb4,
  0x77, 0x0f, 0x0d, 0x0c, 0xcd, 0xf1, 0x78, 0x3c, 0xde, 0x64, 0xea, 0x4b, 0xfd, 0x05, 0x19, 0x29,
  0xe3, 0x73, 0xcd, 0x4b, 0x19, 0x2b, 0xd3, 0x34, 0xa3, 0x27, 0x4e, 0x9c, 0xa8, 0xaf, 0xad, 0xad,
  0xdd, 0x5c, 0x5d, 0x5d, 0x5d, 0xfb, 0xc9, 0x27, 0x9f, 0x34, 0x01, 0x38, 0x03, 0x20, 0x02, 0x80,
  0x06, 0xd6, 0x2c, 0xdf, 0x2e, 0x38, 0x51, 0xe4, 0x07, 0x90, 0x0b, 0xdd, 0x87, 0x99, 0x0c, 0xe0,
  0x66, 00, 0x3f, 0x2f, 0x2c, 0x2c, 0x7c, 0x64, 0xe9, 0xd2, 0xa5, 0xaf, 0xee, 0xfc, 0x74, 0xe7,
  0x3e, 0x33, 0x6a, 0xc6, 0x88, 0xc8, 0xa9, 0x26, 0xeb, 0x04, 0xf5, 0xcb, 0x37, 0xf7, 0xaf, 0x35,
  0x26, 0x26, 0x45, 0x92, 0x64, 0x38, 0x1c, 0xee, 0xa8, 0xdd, 0x51, 0xfb, 0x4e, 0x52, 0xce, 0x33,
  0x14, 0x97, 0x21, 0xe7, 0xf9, 0x36, 0xa8, 0x0e, 0x87, 0xee, 0xbc, 0xd0, 0x4a, 0x68, 0x08, 0xb4,
  0xa3, 0x86, 0x01, 0x18, 0x51, 0x5c, 0x5c, 0x7c, 0xd5, 0x7d, 0xf7, 0xdd, 0x37, 0x6d, 0xfe, 0x8f,
  0xe7, 0x4f, 0x2b, 0x29, 0x2e, 0x19, 0x17, 0x08, 0x04, 0x72, 0x5c, 0x86, 0xcb, 0x21, 0xb8, 0x4b,
  0xba, 0x7e, 0x87, 0xde, 0x44, 0x5c, 0x84, 0x9b, 0x9a, 0x9b, 0xf6, 0x6c, 0xde, 0xbc, 0x79, 0xe3,
  0x0b, 0xab, 0x5f, 0xd8, 0x76, 0xb4, 0xf9, 0xe8, 0x11, 0x68, 0x6a, 0x1b, 0x34, 0x70, 0x41, 0x3b,
  0x27, 0x17, 0xc0, 0x28, 00, 0xa5, 00, 0x66, 0x03, 0xb8, 0xb3, 0xa0, 0xa0, 0xe0, 0xa1, 0x8a,
  0x8a, 0x8a, 0x97, 0x1b, 0xf6, 0x35, 0x1c, 0x8e, 0xc5, 0x62, 0xa6, 0x62, 0x45, 0x4e, 0x36, 0x7f,
  0x56, 0x95, 0x60, 0x80, 0x23, 0x48, 0x27, 0x65, 0x52, 0xac, 0x5e, 0xbd, 0xba, 0x1c, 0xba, 0xd4,
  0x34, 0x68, 0xe1, 0x46, 0x5f, 0x07, 0x4d, 0x06, 0xf0, 0x43, 00, 0xf7, 0x94, 0x95, 0x95, 0xfd,
  0xe1, 0xdd, 0x77, 0xdf, 0xdd, 0xd2, 0xd6, 0xda, 0xd6, 0x91, 0xd4, 0xc3, 0xe1, 0x0b, 0x35, 0xf2,
  0xb9, 0xc6, 0x5f, 0x36, 0xaf, 0xa5, 0xa5, 0xe5, 0x10, 0x74, 0xdb, 0x20, 0x0d, 0x1b, 0xce, 0x1e,
  0x54, 00, 0x60, 0x3c, 0x80, 0x99, 00, 0xee, 0x28, 0x28, 0x28, 0x78, 0x74, 0x55, 0xe5, 0xaa,
  0x37, 0x1b, 0x8f, 0x35, 0x1e, 0x17, 0x42, 0x58, 0xcc, 0xdc, 0xb7, 0x1a, 0x3d, 0x40, 0x60, 0x66,
  0x5e, 0xb6, 0x6c, 0xd9, 0xaf, 0xa1, 0x05, 0x4a, 0x1a, 0x29, 0x70, 0xe8, 0x2d, 0x0f, 0xba, 0x1a,
  0x3c, 0x15, 0xc0, 0x3c, 0xbf, 0xdf, 0xbf, 0xb4, 0xa2, 0xa2, 0xe2, 0x4f, 0x35, 0x5b, 0x6a, 0xf6,
  0x84, 0x42, 0xa1, 0x70, 0x72, 0x05, 0x66, 00, 0xe8, 0x8b, 0xa5, 0x25, 0x63, 0x8b, 0x6e, 0x5f,
  0xb4, 0x10, 0x3a, 0x67, 0x4b, 0xe3, 0x3c, 0x30, 0xa0, 0x25, 0x76, 0x16, 0x74, 0xd5, 0xa1, 0x14,
  0xc0, 0x5c, 00, 0x8b, 0x4b, 0xc7, 0x97, 0x3e, 0xf1, 0xd2, 0x4b, 0x2f, 0x7d, 0xd8, 0xd1, 0xd2,
  0xd1, 0x69, 0x17, 0x42, 0xd9, 0xe9, 0xe9, 0x5c, 0x14, 0x65, 0xb1, 0xdd, 0x3d, 0x60, 0x45, 0x22,
  0x2e, 0x7a, 0xee, 0xbf, 0xff, 0xfe, 0x5f, 0x42, 0x17, 0x36, 0xaf, 0xd8, 0xaa, 0xf3, 0x40, 0x21,
  0x59, 0x5e, 0x07, 0xa0, 0xef, 0x23, 0x9b, 0x0e, 0xe0, 0xa7, 0xa3, 0xc7, 0x8c, 0x7e, 0x64, 0xc5,
  0xaa, 0x15, 0xd5, 0xf5, 0xf5, 0xf5, 0x87, 0x63, 0xb1, 0x78, 0xdc, 0x71, 0x4e, 0xbf, 0x0e, 0x61,
  0x3d, 0x89, 0x24, 0x59, 0x44, 0x44, 0x91, 0x48, 0x24, 0x22, 0xa5, 0x24, 0x62, 0xa6, 0x2f, 0xbe,
  0xf8, 0xe2, 0xd3, 0x15, 0x2b, 0x56, 0x2c, 0xcd, 0xcf, 0xcf, 0xbf, 0x16, 0x40, 0x11, 0xb4, 0x42,
  0xcc, 0x87, 0xde, 0xf3, 0x72, 0xec, 0x23, 0xcb, 0x5e, 0x4b, 0xbf, 0x05, 0xcf, 0x6f, 0x83, 0xec,
  0xbd, 0x5c, 0x70, 0xa2, 0xc7, 0xa9, 0x1e, 0x0c, 0x05, 0x30, 0xc2, 0xeb, 0xf5, 0x8e, 0x99, 0x71,
  0xc3, 0x8c, 0xd2, 0x95, 0x2b, 0x56, 0xde, 0x3a, 0x77, 0xd6, 0xdc, 0x29, 0xd9, 0x39, 0xd9, 0x43,
  0xe0, 0x32, 0x5c, 0xd0, 0x79, 0x29, 00, 0x40, 0xb1, 0x52, 0x49, 0x96, 0x32, 00, 0x38, 0x95,
  0x66, 0xe9, 0xf1, 0x7a, 0x7d, 0x86, 0x3e, 0x67, 0x66, 0x56, 0xf1, 0x78, 0xbc, 0x3b, 0x18, 0x0c,
  0xb6, 0x05, 0x83, 0xc1, 0x0e, 0x33, 0x66, 0x86, 0x62, 0x14, 0x13, 0x99, 0xae, 0x4c, 0x43, 0x90,
  0xb0, 0x9a, 0x9b, 0x9b, 0x9b, 0xde, 0x7b, 0xef, 0xbd, 0xcf, 0x6a, 0x6a, 0x6a, 0x0e, 0x74, 0x76,
  0x76, 0x9e, 0x84, 0xae, 0xa9, 0xa5, 0x61, 0xc3, 0x0d, 0x9d, 0xf3, 0x0c, 0x03, 0x30, 0x01, 0xc0,
  0x8d, 00, 0xca, 0xae, 0x9b, 0x71, 0xdd, 0x13, 0x2f, 0xbe, 0xf8, 0xe2, 0xfa, 0xc6, 0x63, 0x8d,
  0xc7, 0x89, 0x48, 0xda, 0x2d, 0x83, 0x44, 0xc5, 0x39, 0x51, 0x9d, 0xee, 0xbd, 0x7d, 0xe0, 0x5c,
  0x7b, 0x8c, 0x43, 0x6d, 0x44, 0x49, 0x05, 0x6e, 0xfb, 0x10, 0x71, 0x11, 0xde, 0xb1, 0x63, 0x47,
  0x35, 0xb4, 0x20, 0xb9, 0x62, 0xfa, 0x37, 0x03, 0x89, 0x64, 0x61, 0x50, 0x04, 0xe0, 0x5a, 00,
  0xb7, 0x4f, 0x9e, 0x3c, 0xf9, 0xc9, 0xfa, 0xfa, 0xfa, 0xc3, 0x91, 0x68, 0x24, 0xda, 0x6f, 0x5f,
  0xa6, 0x97, 0xd2, 0x12, 0x46, 0x4f, 0x9e, 0x93, 0xdc, 0x3e, 0x70, 0x7a, 0x42, 0x4a, 0x29, 0x3e,
  0x76, 0xec, 0xd8, 0x76, 0xe8, 0xfd, 0x27, 0x8d, 0x7e, 0x60, 0x40, 0x27, 0x7d, 0x05, 0xd0, 0xa2,
  0xe0, 0x16, 0xaf, 0xd7, 0xfb, 0xab, 0x25, 0x0f, 0x2c, 0x79, 0xbe, 0xa6, 0xa6, 0x66, 0x57, 0x38,
  0x14, 0x0e, 0xf7, 0xb1, 0x35, 0x31, 0x93, 0xd3, 0x6d, 0xd3, 0x1d, 0x55, 0x4b, 0x25, 0xdd, 0x47,
  0x98, 0xec, 0x30, 0x45, 0x7d, 0xa2, 0x46, 0x32, 0xb3, 0xb5, 0x60, 0xc1, 0x82, 0x9f, 0xe0, 0x0a,
  0x68, 0xa4, 0x5d, 0x2e, 0x78, 0xa0, 0x69, 0x6d, 0x38, 0x80, 0x89, 00, 0xe6, 00, 0xb8, 0x6b,
  0x62, 0xe9, 0xc4, 0x27, 0xd7, 0xad, 0x5b, 0xf7, 0xef, 0xce, 0xce, 0xce, 0x33, 0x76, 0xfb, 0x99,
  0x99, 0x99, 0xa2, 0xd1, 0x68, 0xdc, 0x12, 0x96, 0x38, 0x2b, 0x84, 0x12, 0x9e, 0xe9, 0xa3, 0x11,
  0x88, 0x48, 0xbe, 0xf2, 0xca, 0x2b, 0xbf, 0xc7, 0x20, 0xcf, 0xfa, 0xbf, 0x2a, 0x5c, 0xd0, 0x39,
  0x47, 00, 0xc0, 0x38, 00, 0x33, 00, 0x2c, 0x9c, 0x30, 0x61, 0xc2, 0xa3, 0x4f, 0x3d, 0xf5,
  0xd4, 0xda, 0xfd, 0x07, 0xf6, 0x1f, 0xb5, 0x2c, 0x4b, 0xa4, 0x08, 0xb3, 0xf3, 0x52, 0x9c, 0x94,
  0xd2, 0xea, 0x09, 0xf5, 0x74, 0x57, 0x56, 0x56, 0x96, 0x23, 0x7d, 0x93, 0xe0, 0xd7, 0x86, 0x0b,
  0xfa, 0x57, 0x9d, 0x03, 0x9d, 0x8c, 0x4e, 0x01, 0x30, 0xdf, 0xed, 0x76, 0x3f, 0x50, 0x56, 0x56,
  0xb6, 0x66, 0xe7, 0xae, 0x9d, 0x0d, 0xd1, 0x68, 0x34, 0x96, 0xe8, 0x56, 0xab, 0xa4, 0x7d, 0x45,
  0xb7, 0xaf, 0x89, 0x88, 0xa5, 0x90, 0xf1, 0x40, 0x20, 0x30, 0x05, 0x83, 0x4b, 0xed, 0x5e, 0x16,
  0x78, 0xa0, 0xa5, 0x74, 0x21, 0x12, 0xc9, 0xe8, 0xdd, 0xf3, 0xe6, 0xcd, 0xab, 0x7c, 0xe7, 0xad,
  0x77, 0x3e, 0x6a, 0x6d, 0x6d, 0x3d, 0xcd, 0xc4, 0x52, 0x91, 0xe2, 0xf6, 0x96, 0xf6, 0x2e, 0x29,
  0xa4, 0x8c, 0xc7, 0xe3, 0xd1, 0x35, 0x6b, 0xd6, 0x3c, 0x8c, 0x94, 0xf2, 0x4b, 0xda, 0x33, 0x03,
  0x0f, 0x37, 0x74, 0xd6, 0x9e, 0x09, 0xad, 0xda, 0x86, 0x03, 0x18, 0xe9, 0x76, 0xbb, 0x87, 0xcf,
  0xbf, 0x75, 0x7e, 0xd1, 0xb4, 0xa9, 0xd3, 0xf2, 0x0f, 0x7d, 0x7e, 0xa8, 0x71, 0xe3, 0x07, 0x1b,
  0xff, 0x03, 0xe0, 0xbf, 0x48, 0x29, 0xe3, 0xa7, 0x1d, 0x72, 0x69, 0x61, 0x40, 0x3b, 0xc7, 0x07,
  0x2d, 0xab, 0xdd, 0xd0, 0xdd, 0xc7, 0x98, 0x7d, 0xa4, 0xff, 0x2d, 0x30, 0x8d, 0x34, 0xd2, 0xf8,
  0x2e, 0xe3, 0xff, 0x4f, 0x2e, 0x20, 0xbe, 0xba, 0x4b, 0x28, 0x4f, 00, 00, 00, 00, 0x49,
  0x45, 0x4e, 0x44, 0xae, 0x42, 0x60, 0x82
};


