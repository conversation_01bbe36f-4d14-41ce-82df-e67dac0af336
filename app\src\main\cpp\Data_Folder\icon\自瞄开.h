//c写法 养猫牛逼

static const unsigned char 自瞄开[2401] ={0x89, 0x50, 0x4E, 0x47, 0xD, 0xA, 0x1A, 0xA, 0x0, 0x0, 0x0, 0xD, 0x49, 0x48, 0x44, 0x52, 0x0, 0x0, 0x2, 0x0, 0x0, 0x0, 0x2, 0x0, 0x8, 0x3, 0x0, 0x0, 0x0, 0xC3, 0xA6, 0x24, 0xC8, 0x0, 0x0, 0x0, 0x6, 0x50, 0x4C, 0x54, 0x45, 0x0, 0x0, 0x0, 0x9B, 0xCD, 0x3F, 0x9C, 0x13, 0x6A, 0xE0, 0x0, 0x0, 0x0, 0x1, 0x74, 0x52, 0x4E, 0x53, 0x0, 0x40, 0xE6, 0xD8, 0x66, 0x0, 0x0, 0x9, 0x9, 0x49, 0x44, 0x41, 0x54, 0x78, 0xDA, 0xED, 0xDD, 0x49, 0x76, 0x23, 0x57, 0xC, 0x4, 0xC0, 0xC2, 0xFD, 0x2F, 0xED, 0x9D, 0x57, 0x1E, 0xD4, 0xAA, 0x1, 0xC0, 0xCF, 0xC0, 0xDE, 0x8F, 0x40, 0x66, 0xB8, 0x68, 0xB9, 0xD9, 0xD4, 0x75, 0xE5, 0x4E, 0xFD, 0x3D, 0x97, 0xC9, 0x2D, 0x1F, 0x2, 0xED, 0x33, 0xA0, 0x7E, 0x4, 0xF4, 0x4F, 0x80, 0xFA, 0x11, 0xD0, 0x3F, 0x1, 0xE9, 0xFD, 0x13, 0x90, 0xDE, 0x3F, 0x1, 0xE9, 0xFD, 0x13, 0x90, 0xDE, 0x3F, 0x1, 0xE9, 0xFD, 0x13, 0x90, 0xDE, 0x3F, 0x1, 0x0, 0x98, 0xE8, 0xFE, 0x9, 0x0, 0xC0, 0x44, 0xF7, 0x4F, 0x0, 0x0, 0x26, 0xBA, 0x7F, 0x2, 0x0, 0x30, 0x0, 0x98, 0xDC, 0xFE, 0x9, 0x0, 0xC0, 0x0, 0x60, 0x0, 0x30, 0x0, 0x18, 0x0, 0xC, 0x0, 0x6, 0x0, 0x3, 0x80, 0x1, 0xC0, 0x0, 0x60, 0x0, 0x30, 0x0, 0x18, 0x0, 0xC, 0x0, 0x6, 0x0, 0x3, 0x80, 0x1, 0xC0, 0x0, 0x60, 0x0, 0x30, 0x0, 0x18, 0x0, 0xC, 0x0, 0x6, 0x0, 0x3, 0x80, 0x1, 0xC0, 0x0, 0x60, 0x0, 0x30, 0x0, 0x18, 0x0, 0xC, 0x0, 0x6, 0x0, 0x3, 0x80, 0x1, 0xC0, 0x0, 0x60, 0x0, 0x30, 0x0, 0x18, 0x0, 0xC, 0x0, 0x66, 0x50, 0xF9, 0x10, 0xE8, 0x1E, 0x3, 0xDD, 0x53, 0xA0, 0x7C, 0x8, 0x94, 0xF, 0x81, 0xF2, 0x21, 0xD0, 0x3E, 0x3, 0xDA, 0x67, 0x40, 0xFB, 0xC, 0x68, 0x9F, 0x1, 0xED, 0x33, 0xA0, 0x7D, 0x4, 0xD4, 0xCF, 0x80, 0xFA, 0x11, 0xD0, 0x3E, 0x3, 0xEA, 0x47, 0x40, 0xFD, 0x8, 0xA8, 0x1F, 0x1, 0xF5, 0x23, 0xA0, 0x7E, 0x4, 0xD4, 0x8F, 0x80, 0xFA, 0x11, 0x50, 0x3F, 0x2, 0xEA, 0x47, 0x40, 0xFD, 0x4, 0xC4, 0xF7, 0x8F, 0x40, 0x7A, 0xFD, 0x8, 0xE8, 0x1F, 0x81, 0xF4, 0xFA, 0x9, 0x48, 0xAF, 0x1F, 0x1, 0xFD, 0x13, 0x10, 0x5E, 0x3F, 0x2, 0xFA, 0x27, 0x20, 0xBC, 0x7E, 0x4, 0xF4, 0x4F, 0x40, 0x7A, 0xFF, 0x8, 0xA4, 0xD7, 0x4F, 0x40, 0x7C, 0xFF, 0x4, 0x84, 0xD7, 0x8F, 0x80, 0xFE, 0x9, 0x48, 0xEF, 0x9F, 0x80, 0xF4, 0xFE, 0x9, 0x8, 0xAF, 0x1F, 0x1, 0xFD, 0x13, 0x90, 0xDE, 0x3F, 0x1, 0xE9, 0xFD, 0xC7, 0xB, 0x28, 0xA3, 0x7E, 0x4, 0xF4, 0x4F, 0x80, 0xFE, 0x9, 0xD0, 0x3F, 0x1, 0xFA, 0x27, 0x40, 0xFF, 0x4, 0xE8, 0x9F, 0x0, 0xFD, 0x13, 0xA0, 0x7F, 0x2, 0xF4, 0x4F, 0x80, 0xFE, 0x9, 0xD0, 0xFF, 0xDD, 0x48, 0x3B, 0xFE, 0x59, 0x2, 0x6, 0xF4, 0x7F, 0xF7, 0x25, 0x3B, 0xC8, 0xEA, 0xFF, 0x85, 0x24, 0x9F, 0x28, 0x83, 0x80, 0x4D, 0xFD, 0x3F, 0xF4, 0xA2, 0x23, 0x96, 0xD7, 0xFF, 0xFD, 0x0, 0x1F, 0xAC, 0x82, 0x80, 0xE9, 0xFD, 0x3F, 0xF9, 0xB2, 0x93, 0x6E, 0xD0, 0xFF, 0x8D, 0xE4, 0x9E, 0x2E, 0x82, 0x80, 0x91, 0xFD, 0x3F, 0xFF, 0xC2, 0x7D, 0x94, 0x1, 0x78, 0x34, 0xB2, 0x57, 0x6A, 0x0, 0x60, 0x52, 0xFF, 0xEF, 0xBC, 0xF2, 0xD8, 0x83, 0xF4, 0xFF, 0x67, 0x69, 0xBD, 0xD6, 0x2, 0x1, 0x13, 0xFA, 0x7F, 0xEF, 0xB5, 0x87, 0x9F, 0xA5, 0xFF, 0x9F, 0xE6, 0xF4, 0x6A, 0x7, 0x4, 0x74, 0xA6, 0xF4, 0xEE, 0xAB, 0xAF, 0x38, 0x2E, 0x19, 0xC0, 0xDB, 0xAF, 0xBE, 0xE4, 0xBC, 0xD4, 0xFE, 0xDF, 0x7F, 0xF9, 0x3D, 0x17, 0xEA, 0xBF, 0x1B, 0x0, 0x1, 0x1F, 0x67, 0xF3, 0xC9, 0x2, 0xCB, 0xCE, 0xC, 0xEA, 0xFF, 0xA3, 0xD, 0xF6, 0x5D, 0x1A, 0x2, 0xE0, 0x1A, 0xA, 0xE0, 0x2, 0xE0, 0x8B, 0x50, 0xBE, 0x5B, 0x61, 0xE7, 0xB5, 0xFA, 0xEF, 0x4, 0x40, 0xC0, 0xCB, 0x81, 0x7C, 0xBA, 0xC3, 0xDE, 0x8B, 0xF5, 0xDF, 0x8, 0x80, 0x80, 0xD7, 0xB2, 0xF8, 0x7A, 0x8B, 0xE5, 0x67, 0x1F, 0x7, 0xE0, 0x5A, 0x3, 0x80, 0x80, 0x51, 0xFD, 0x37, 0x0, 0xB8, 0x0, 0x98, 0x94, 0x42, 0x47, 0xF0, 0x4, 0x3C, 0x1B, 0x42, 0xCB, 0x1E, 0x73, 0xF8, 0xEB, 0x7F, 0x21, 0x80, 0x2B, 0x1D, 0xC0, 0x9C, 0xFB, 0xBB, 0x62, 0xCF, 0x16, 0x30, 0xE9, 0xFA, 0xAE, 0xD0, 0xA3, 0x5, 0x4C, 0x3A, 0x7D, 0xD9, 0xB, 0x1F, 0x1, 0x60, 0xD4, 0xE5, 0x8D, 0x99, 0xC7, 0xA, 0x98, 0x75, 0x77, 0x63, 0xE2, 0xA1, 0x0, 0x86, 0x9D, 0xDD, 0x99, 0x78, 0xA4, 0x80, 0x69, 0x47, 0xB7, 0x6, 0x9E, 0x28, 0x60, 0xDC, 0xC9, 0xAD, 0x71, 0xE7, 0x1, 0x98, 0x77, 0x71, 0x6F, 0xDC, 0x71, 0x2, 0x6, 0xDE, 0xDB, 0x1B, 0x76, 0x18, 0x80, 0x91, 0xE7, 0xF6, 0x66, 0x1D, 0x25, 0x60, 0xE6, 0xB1, 0xCD, 0x51, 0x27, 0x9, 0x18, 0x7A, 0x6A, 0x73, 0xD0, 0x39, 0x0, 0xC6, 0x5A, 0x6F, 0xCE, 0x39, 0x46, 0xC0, 0xDC, 0x43, 0x7B, 0x53, 0x4E, 0x1, 0x30, 0xF9, 0xCE, 0xDE, 0x90, 0x43, 0x4, 0x8C, 0xBE, 0xB2, 0x37, 0xE2, 0x8, 0x0, 0xD3, 0x8F, 0x6C, 0xD, 0x38, 0x41, 0xC0, 0xF8, 0x13, 0x5B, 0xE3, 0x3D, 0x1F, 0xC0, 0x86, 0xB, 0x3B, 0xC3, 0x3D, 0x5E, 0xC0, 0x8E, 0x3, 0xFB, 0x92, 0x3D, 0x1D, 0xC0, 0x9E, 0xFB, 0xBA, 0x62, 0x3D, 0x5C, 0xC0, 0xB2, 0xEB, 0x3A, 0x2, 0x3D, 0x1A, 0x40, 0xE8, 0x7, 0xE0, 0x85, 0xF4, 0xD8, 0x6D, 0x17, 0x0, 0x9B, 0x53, 0xF2, 0x0, 0x8, 0x8F, 0x49, 0xFF, 0xD9, 0x39, 0xE9, 0x3F, 0x3C, 0x29, 0x0, 0x0, 0xD0, 0x7F, 0x70, 0x54, 0xFA, 0xF, 0xF, 0xB, 0x80, 0xEC, 0xB0, 0xF4, 0x1F, 0x1E, 0x97, 0xFE, 0xC3, 0xF3, 0x2, 0x20, 0x3B, 0x2F, 0xFD, 0x87, 0x27, 0x6, 0x40, 0x76, 0x62, 0xFA, 0xF, 0xCF, 0xC, 0x80, 0xCF, 0x43, 0x73, 0xB, 0x0, 0x4E, 0x21, 0xC0, 0x25, 0x0, 0xAC, 0xBF, 0xE4, 0xBA, 0x8, 0x58, 0x9E, 0x9B, 0x7, 0x40, 0x78, 0x70, 0xFA, 0xCF, 0x4E, 0xCE, 0x3, 0x20, 0x3C, 0x3A, 0xFD, 0x87, 0x67, 0x7, 0x0, 0x0, 0xFA, 0xF, 0xE, 0xCF, 0x3, 0x20, 0x3C, 0xBD, 0xAD, 0x17, 0xC, 0xC9, 0x11, 0x80, 0x29, 0xB, 0x8B, 0x2F, 0xE1, 0x19, 0x36, 0x2F, 0xD2, 0xF5, 0xEF, 0x1, 0x8B, 0xF6, 0x9F, 0x19, 0x2A, 0x0, 0xE3, 0x16, 0x15, 0xE0, 0x79, 0xEB, 0x9F, 0x21, 0x73, 0xA2, 0x80, 0x15, 0xDB, 0xCF, 0x8E, 0x36, 0x16, 0xC0, 0xF4, 0x15, 0x1, 0x38, 0x3, 0xC0, 0xB9, 0x42, 0x27, 0x0, 0x18, 0xAF, 0x77, 0xC3, 0x9F, 0xB7, 0x6D, 0x7E, 0x4, 0x9C, 0xDD, 0xFF, 0x2, 0x1, 0x0, 0xBC, 0x59, 0xFF, 0x57, 0x9, 0x3, 0x30, 0xB7, 0xFF, 0xE9, 0x50, 0xF5, 0xFF, 0x76, 0xFF, 0x4, 0x6C, 0x5D, 0xBC, 0x9E, 0x1B, 0x0, 0xF6, 0x2D, 0x5E, 0x8F, 0xE, 0x0, 0xDB, 0x7E, 0xC6, 0xAE, 0x4A, 0x11, 0x0, 0xC0, 0x17, 0xFD, 0xF, 0x6, 0xEB, 0x1D, 0xE0, 0x93, 0xFE, 0x7, 0x8B, 0xF5, 0x0, 0xF8, 0xA4, 0xFF, 0xB9, 0x2B, 0x3, 0x70, 0x2, 0x80, 0xB, 0x80, 0xE9, 0xFD, 0x8F, 0x45, 0xB, 0xC0, 0x47, 0xFD, 0x4F, 0x55, 0xAB, 0xFF, 0xAF, 0xFA, 0x27, 0x60, 0xC3, 0xCE, 0x55, 0x4B, 0x5, 0x0, 0x0, 0x0, 0x0, 0xD3, 0xFB, 0x9F, 0x29, 0x77, 0x21, 0x80, 0xB, 0x80, 0x15, 0x69, 0xEE, 0x5B, 0xB9, 0x2A, 0xF0, 0x11, 0xE0, 0x1D, 0xE0, 0xC3, 0xFE, 0x47, 0xE2, 0xDD, 0x19, 0xE4, 0x1B, 0x7, 0xC5, 0x2F, 0xF7, 0xC4, 0xDE, 0x35, 0x62, 0x6, 0x47, 0xBC, 0xB1, 0xFD, 0x9F, 0xAF, 0x5E, 0x73, 0xE6, 0x1C, 0x0, 0x7B, 0x42, 0xAD, 0x51, 0x33, 0x35, 0xE7, 0xC5, 0xFD, 0xFF, 0xF7, 0xF6, 0x35, 0x6E, 0x4E, 0x0, 0xB0, 0x27, 0xD5, 0xAA, 0xDD, 0x2, 0xD2, 0xB7, 0xBA, 0xBB, 0x7F, 0xCD, 0x9C, 0xDD, 0x0, 0x16, 0x85, 0x5A, 0xB5, 0x5D, 0x80, 0x9F, 0xA8, 0xEE, 0x5C, 0x50, 0xB5, 0x5D, 0x40, 0xF8, 0x4A, 0x37, 0x2F, 0xA8, 0x5A, 0x2F, 0x20, 0x7B, 0xA3, 0x9B, 0x27, 0x54, 0xED, 0x17, 0x10, 0xBD, 0xD0, 0xCD, 0x1B, 0xEA, 0x0, 0x0, 0xE9, 0x1B, 0xDD, 0xBA, 0xA1, 0xE, 0x10, 0x10, 0xBE, 0xD0, 0xAD, 0x1B, 0xAA, 0xE, 0x10, 0x90, 0xBD, 0xCF, 0xAD, 0x23, 0xA, 0x80, 0x17, 0x16, 0x5A, 0x94, 0x6A, 0x9D, 0x20, 0x20, 0x7D, 0x9F, 0x1B, 0x47, 0x14, 0x0, 0xA9, 0x0, 0x6A, 0xD1, 0xAA, 0xB5, 0xB, 0x40, 0x1, 0x0, 0xC0, 0x92, 0x50, 0xB, 0x80, 0x5C, 0x0, 0xB5, 0x68, 0xD5, 0x1A, 0x16, 0xF8, 0x11, 0xFD, 0x3, 0x0, 0x0, 0x0, 0xE1, 0x0, 0xA, 0x80, 0x64, 0x0, 0x5, 0x0, 0x0, 0x47, 0x8, 0x48, 0xDF, 0x6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xFC, 0x18, 0xE8, 0xC7, 0x40, 0x0, 0x0, 0xF8, 0xD9, 0x19, 0x0, 0x24, 0x3, 0xF0, 0x67, 0x1, 0x0, 0x9C, 0x1, 0xE0, 0xCA, 0xDE, 0x26, 0x1, 0x80, 0xF, 0x84, 0xBC, 0x75, 0x6, 0x0, 0xB9, 0x0, 0x7C, 0x26, 0x10, 0x0, 0x1F, 0xB, 0x3F, 0x63, 0x9F, 0x3B, 0x57, 0x0, 0x90, 0xA, 0xC0, 0x5F, 0xD, 0x3B, 0x66, 0x9F, 0x7B, 0x57, 0x0, 0x90, 0x9, 0xC0, 0x5F, 0xF, 0x3F, 0x68, 0xA1, 0x7B, 0x37, 0x1C, 0xD0, 0xBF, 0x6F, 0x8, 0xB9, 0x75, 0x4, 0x0, 0x79, 0x0, 0x7C, 0x49, 0xD4, 0x51, 0x1B, 0xDD, 0x3E, 0x61, 0x7D, 0xFF, 0x13, 0xBF, 0x27, 0x70, 0x55, 0xA8, 0xEB, 0xFB, 0xF7, 0x45, 0x91, 0x37, 0x2F, 0xD8, 0xDE, 0xBF, 0xAF, 0x8A, 0xBD, 0x7B, 0xC0, 0xF2, 0xFE, 0x67, 0x7E, 0x5B, 0xF8, 0xAA, 0x50, 0x77, 0xD7, 0xEF, 0xEB, 0xE2, 0x1F, 0xD8, 0x7F, 0x75, 0xFF, 0x53, 0x7F, 0x63, 0xC8, 0xAE, 0x50, 0x17, 0xD7, 0xEF, 0x57, 0xC6, 0x3C, 0xB3, 0xFC, 0xD6, 0xF6, 0xBF, 0xDA, 0x7D, 0xEE, 0x66, 0xE7, 0xFC, 0xF8, 0x72, 0xFD, 0x76, 0xE2, 0x97, 0x7B, 0x63, 0xEF, 0x37, 0x2E, 0xBA, 0xDE, 0x9A, 0x95, 0x39, 0xCE, 0x8D, 0x13, 0x0, 0x0, 0xDE, 0x59, 0xF9, 0x5A, 0x2B, 0x20, 0x2B, 0xCD, 0x8D, 0x66, 0xF3, 0xFA, 0x7, 0x0, 0x0, 0x0, 0x3E, 0x12, 0x70, 0x1, 0xB0, 0x60, 0xE7, 0x9D, 0xFD, 0x87, 0x1, 0x58, 0x29, 0x20, 0x31, 0xCB, 0x9D, 0x6A, 0x3D, 0x0, 0x0, 0xD8, 0xD6, 0x3F, 0x0, 0xE3, 0x5, 0x5C, 0x0, 0x2C, 0x7A, 0xE3, 0xA, 0xEA, 0x1F, 0x80, 0x4F, 0x4, 0x5C, 0x0, 0x2C, 0x7B, 0x70, 0xED, 0xEA, 0x7F, 0xE9, 0x3B, 0xC0, 0x68, 0xB8, 0x21, 0xFD, 0x3, 0xF0, 0x3E, 0x81, 0xF4, 0x1C, 0xF7, 0x2E, 0xBE, 0xA6, 0xFF, 0xBD, 0x0, 0xAE, 0x63, 0x73, 0xD5, 0xFF, 0x11, 0xAB, 0xEF, 0xE8, 0x1F, 0x80, 0xB1, 0x4, 0xA4, 0xF8, 0x66, 0xBE, 0xDE, 0xA5, 0xF6, 0x84, 0xB8, 0x1A, 0xEF, 0xF8, 0x6C, 0x37, 0xF7, 0xBF, 0x63, 0xF9, 0xE1, 0xD1, 0xA6, 0x2, 0x98, 0x9C, 0xB1, 0x8, 0x8F, 0xDB, 0x7E, 0x6E, 0xAC, 0xBB, 0x1, 0x6C, 0x7A, 0x7E, 0xD, 0xCD, 0x74, 0x77, 0xFF, 0xCB, 0xFC, 0xE, 0x4C, 0xB4, 0x0, 0x18, 0xB2, 0xB1, 0x0, 0xBF, 0xFF, 0x3F, 0x2D, 0x23, 0xF6, 0x96, 0x5F, 0xB6, 0x60, 0xF1, 0xC5, 0x13, 0x96, 0x1E, 0x0, 0xD2, 0x23, 0x40, 0x76, 0x0, 0xC8, 0xE, 0x0, 0xD9, 0x11, 0x20, 0x39, 0x0, 0x36, 0x4, 0x57, 0xEE, 0x0, 0xC0, 0x21, 0xFA, 0x77, 0x9, 0x0, 0xDB, 0x2F, 0x29, 0xFD, 0xAF, 0x4F, 0xD, 0x80, 0xF0, 0xD0, 0x3C, 0x2, 0xD2, 0x33, 0x3, 0x20, 0x3C, 0x32, 0x8F, 0x80, 0xF0, 0xC4, 0x0, 0xF8, 0x36, 0xB0, 0x2, 0xDA, 0x3, 0x80, 0x68, 0xF, 0x0, 0xA4, 0x3D, 0x0, 0x98, 0xF6, 0x0, 0x38, 0xE3, 0xA6, 0xD2, 0xFF, 0xF2, 0xAC, 0x0, 0x8, 0x8F, 0xCA, 0x23, 0x20, 0x3D, 0x29, 0x2, 0xC2, 0x73, 0x2, 0x20, 0x3D, 0x27, 0x2, 0xC2, 0x53, 0x2A, 0x2, 0xC2, 0x43, 0x5A, 0x76, 0xDC, 0xB6, 0x6F, 0x83, 0x98, 0xFF, 0x2F, 0xC9, 0x9E, 0xEB, 0xDA, 0x52, 0x3D, 0xFC, 0x29, 0xB9, 0xE3, 0xBC, 0xC6, 0x60, 0x4F, 0x7F, 0x97, 0xAC, 0x5, 0x7, 0x76, 0x66, 0x5B, 0xC7, 0xFF, 0x67, 0xD2, 0xF8, 0xB, 0x5B, 0xD3, 0x3D, 0xBF, 0xFF, 0xF1, 0x5F, 0xCF, 0xDC, 0x9A, 0x6F, 0x5, 0x0, 0x98, 0x8D, 0xBC, 0x39, 0xE1, 0x84, 0xFE, 0x47, 0x2B, 0x6F, 0xCE, 0x38, 0xE2, 0x1, 0x30, 0xF9, 0xCC, 0xE6, 0x90, 0x43, 0xFA, 0x7F, 0xE6, 0xD7, 0x74, 0xF4, 0x2E, 0x96, 0x95, 0x4B, 0xCA, 0xA5, 0xCD, 0x39, 0xE7, 0xF4, 0x3F, 0xF4, 0x37, 0xF5, 0x34, 0x27, 0x5D, 0x41, 0x0, 0x66, 0x1E, 0xBB, 0x89, 0xDF, 0xF6, 0x3F, 0x2A, 0xAB, 0x81, 0xE7, 0xF6, 0x66, 0x5D, 0x59, 0x0, 0x6, 0xDE, 0xDB, 0x1B, 0x76, 0x5A, 0xFF, 0x3, 0x2F, 0x5E, 0xA6, 0x6F, 0xFD, 0x67, 0x25, 0x6A, 0xD8, 0xCD, 0xAD, 0x79, 0x57, 0x20, 0x80, 0x69, 0x47, 0x6F, 0xC3, 0xB7, 0xFF, 0xC3, 0x52, 0x35, 0xEB, 0xEC, 0x65, 0xF6, 0x4E, 0xF8, 0xB4, 0xDC, 0xA8, 0xBB, 0x1B, 0x13, 0x4F, 0xED, 0x7F, 0xD6, 0xE5, 0xDB, 0xE8, 0x1D, 0xF1, 0x69, 0xD9, 0x1A, 0x74, 0xFB, 0x32, 0x79, 0x87, 0x7C, 0x5C, 0x7A, 0xCE, 0xF1, 0x6D, 0xA1, 0x47, 0xF7, 0xFF, 0xE0, 0xF5, 0xB5, 0x14, 0x40, 0x76, 0xFF, 0x8F, 0xA, 0xA8, 0x85, 0x0, 0xAA, 0xD2, 0x1, 0x8C, 0x89, 0x60, 0x95, 0xBA, 0x83, 0xFA, 0x7F, 0x34, 0x83, 0x5A, 0x6, 0x40, 0xFF, 0x83, 0x4, 0x2C, 0x32, 0x77, 0x18, 0x80, 0x67, 0x73, 0xA8, 0x35, 0x0, 0x4A, 0xFF, 0xA3, 0xA2, 0xD8, 0x2, 0xEE, 0xC0, 0xFE, 0x1F, 0xCE, 0xA2, 0x56, 0x0, 0x28, 0x0, 0xDE, 0x4B, 0xA3, 0xC6, 0x3, 0x28, 0xFD, 0x8F, 0xB, 0x64, 0x3, 0xB6, 0x63, 0xFB, 0x7F, 0x3C, 0x91, 0x5F, 0x64, 0xF2, 0x5D, 0xF8, 0x3, 0x8E, 0x25, 0xA0, 0x11, 0x80, 0xFE, 0x3F, 0x8A, 0xA5, 0x46, 0x2, 0x28, 0xFD, 0x7F, 0x97, 0x4C, 0x8D, 0x3, 0x50, 0xFA, 0xFF, 0x34, 0x9B, 0x1A, 0x6, 0xA0, 0x0, 0xF8, 0x3A, 0x9D, 0x1A, 0x4, 0xA0, 0xF4, 0xDF, 0x11, 0x50, 0xD, 0x1, 0x50, 0xFA, 0xEF, 0xCA, 0xA8, 0x6, 0x0, 0x28, 0xFD, 0x77, 0xC6, 0x54, 0xCD, 0x0, 0x4A, 0xFF, 0xDD, 0x2, 0xAA, 0x11, 0x40, 0xE9, 0x7F, 0x0, 0x80, 0xFF, 0x4D, 0xEB, 0xAD, 0x12, 0x1A, 0x4F, 0x22, 0xE0, 0x4F, 0xF2, 0x7A, 0xA7, 0x84, 0xD2, 0xFF, 0x24, 0x1, 0xF5, 0x31, 0x80, 0xD2, 0xFF, 0x34, 0x1, 0xFF, 0x1E, 0xDB, 0xE3, 0x35, 0x74, 0x1D, 0x42, 0xC0, 0xEF, 0x92, 0x7B, 0xB8, 0x87, 0xD2, 0xFF, 0x5C, 0x1, 0xFF, 0x98, 0xDE, 0x93, 0x45, 0xF4, 0x5C, 0x40, 0xC0, 0xAD, 0x4, 0x1F, 0x6B, 0xA2, 0x65, 0x7B, 0x2, 0x6E, 0xA7, 0xF8, 0x4C, 0x15, 0x1D, 0x9B, 0x13, 0xF0, 0x48, 0x92, 0xF, 0x74, 0xD1, 0xB0, 0x35, 0x1, 0x8F, 0xC5, 0x79, 0xB3, 0x8C, 0xFA, 0x7E, 0x63, 0x2, 0x9E, 0x8C, 0xF4, 0x4E, 0x19, 0xF5, 0xF5, 0xB2, 0x17, 0x1, 0xC9, 0x73, 0x1, 0xA0, 0x7F, 0x2, 0xF4, 0x4F, 0x80, 0xFE, 0x9, 0xD0, 0x3F, 0x2, 0xEA, 0x27, 0x40, 0xFF, 0x4, 0xE8, 0x9F, 0x0, 0xFD, 0x13, 0xA0, 0x7F, 0x2, 0xF4, 0x4F, 0x80, 0xFE, 0x9, 0xD0, 0x3F, 0x2, 0xEA, 0x27, 0x40, 0xFF, 0x4, 0xE8, 0x9F, 0x0, 0xFD, 0x23, 0xA0, 0x7E, 0x2, 0xF4, 0x4F, 0x80, 0xFE, 0x11, 0x50, 0x3F, 0x1, 0xFA, 0x27, 0x40, 0xFF, 0x8, 0xA8, 0x9F, 0x0, 0xFD, 0x23, 0xA0, 0x7E, 0x2, 0xF4, 0x8F, 0x80, 0xFA, 0x9, 0xD0, 0x3F, 0x2, 0xEA, 0x27, 0x40, 0xFD, 0x8, 0xE8, 0x1F, 0x1, 0xF5, 0x23, 0xA0, 0x7E, 0x2, 0xF4, 0x8F, 0x80, 0xFA, 0x11, 0x50, 0x3F, 0x2, 0xEA, 0x47, 0x40, 0xFD, 0x8, 0xA8, 0x1F, 0x1, 0xF5, 0x23, 0xA0, 0x7E, 0x4, 0xD4, 0xCF, 0x80, 0xF6, 0x11, 0x50, 0x3F, 0x3, 0xEA, 0x47, 0x40, 0xFB, 0xC, 0x68, 0x9F, 0x1, 0xED, 0x23, 0xA0, 0x7E, 0x8, 0x94, 0xCF, 0x80, 0xF6, 0x21, 0xD0, 0x3E, 0x3, 0xCA, 0x4F, 0x47, 0x20, 0xDA, 0x5C, 0x5, 0x2, 0xCD, 0x65, 0x20, 0xC5, 0x58, 0x4, 0xB2, 0xCB, 0x16, 0x20, 0x39, 0x0, 0xC, 0x0, 0x6, 0x0, 0x3, 0x80, 0x1, 0xC0, 0x0, 0x60, 0x0, 0x30, 0x0, 0x18, 0x0, 0xC, 0x0, 0x6, 0x0, 0x3, 0x80, 0x1, 0xC0, 0x0, 0x60, 0x0, 0x30, 0x0, 0x18, 0x0, 0xC, 0x0, 0x6, 0x0, 0x3, 0x80, 0x1, 0xC0, 0x0, 0x60, 0x0, 0x30, 0x0, 0x18, 0x0, 0xC, 0x0, 0x6, 0x0, 0x3, 0x80, 0x1, 0xC0, 0x0, 0x60, 0x0, 0x30, 0x0, 0x18, 0x0, 0xC, 0x0, 0x6, 0x0, 0x3, 0x80, 0x1, 0xC0, 0xC, 0x13, 0x20, 0x37, 0x0, 0xC, 0x0, 0x26, 0x55, 0x80, 0xD4, 0x0, 0x30, 0xC1, 0x2, 0x64, 0x6, 0x80, 0x9, 0x16, 0x20, 0x31, 0x0, 0x4C, 0xB0, 0x0, 0x79, 0x65, 0xB, 0x90, 0x56, 0xB6, 0x0, 0x59, 0x65, 0xB, 0x90, 0x54, 0xB6, 0x0, 0x39, 0x65, 0xB, 0x90, 0x52, 0xB6, 0x0, 0x19, 0x45, 0x13, 0x90, 0x4F, 0xB6, 0x0, 0xE9, 0x44, 0x13, 0x90, 0x4C, 0xB2, 0x1, 0x99, 0x4, 0x23, 0x8, 0xE, 0xE1, 0x2F, 0x70, 0x63, 0x6E, 0x7, 0x1E, 0x22, 0xE6, 0x4E, 0x0, 0x0, 0x0, 0x0, 0x49, 0x45, 0x4E, 0x44, 0xAE, 0x42, 0x60, 0x82, };
