package com.bearmod.debug;

import android.content.Context;
import android.util.Log;
import com.bearmod.BuildConfig;
import java.io.File;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * Comprehensive debug-only diagnostic logging system for bear-inject project
 * 
 * Features:
 * - Debug-only logging using BuildConfig.DEBUG checks
 * - Consistent formatting with bear emoji prefix
 * - Component-specific logging methods
 * - File size and checksum tracking
 * - Thread-safe operations
 * - Zero overhead in release builds
 * 
 * Usage:
 * DevLogger.library().loadSuccess("libbearmod.so", "/path/to/lib", 2048576);
 * DevLogger.script().executionStart("anti-detection.js", "/path/to/script");
 * DevLogger.ota().downloadComplete("patch.dex", 1536000, "abc123hash");
 * DevLogger.injection().mundoCoreInit("1.2.3", true);
 */
public class DevLogger {
    
    // Base configuration
    private static final String BASE_TAG = "🐻[BearInject]";
    private static final boolean DEBUG_ENABLED = BuildConfig.DEBUG;
    private static final AtomicBoolean INITIALIZED = new AtomicBoolean(false);
    
    // Timestamp formatter
    private static final SimpleDateFormat TIMESTAMP_FORMAT = 
        new SimpleDateFormat("HH:mm:ss.SSS", Locale.US);
    
    // Component loggers
    private static final LibraryLogger LIBRARY_LOGGER = new LibraryLogger();
    private static final ScriptLogger SCRIPT_LOGGER = new ScriptLogger();
    private static final OTALogger OTA_LOGGER = new OTALogger();
    private static final InjectionLogger INJECTION_LOGGER = new InjectionLogger();
    private static final FloatingLogger FLOATING_LOGGER = new FloatingLogger();
    
    /**
     * Initialize the debug logger (call once from Application.onCreate)
     */
    public static void initialize(Context context) {
        if (!DEBUG_ENABLED) return;
        
        if (INITIALIZED.compareAndSet(false, true)) {
            String timestamp = TIMESTAMP_FORMAT.format(new Date());
            Log.i(BASE_TAG, String.format("[%s] 🚀 DevLogger initialized - Debug mode active", timestamp));
            Log.i(BASE_TAG, String.format("[%s] 📱 App: %s v%s (%s)", 
                timestamp, context.getPackageName(), BuildConfig.VERSION_NAME, BuildConfig.BUILD_TYPE));
        }
    }
    
    /**
     * Get library loading logger
     */
    public static LibraryLogger library() {
        return LIBRARY_LOGGER;
    }
    
    /**
     * Get script execution logger
     */
    public static ScriptLogger script() {
        return SCRIPT_LOGGER;
    }
    
    /**
     * Get OTA operations logger
     */
    public static OTALogger ota() {
        return OTA_LOGGER;
    }
    
    /**
     * Get injection system logger
     */
    public static InjectionLogger injection() {
        return INJECTION_LOGGER;
    }
    
    /**
     * Get floating overlay logger
     */
    public static FloatingLogger floating() {
        return FLOATING_LOGGER;
    }
    
    // Base logger class with common functionality
    private static abstract class BaseLogger {
        protected final String componentTag;
        
        protected BaseLogger(String component) {
            this.componentTag = String.format("🐻[%s]", component);
        }
        
        protected void logDebug(String message) {
            if (!DEBUG_ENABLED) return;
            String timestamp = TIMESTAMP_FORMAT.format(new Date());
            Log.d(componentTag, String.format("[%s] %s", timestamp, message));
        }
        
        protected void logInfo(String message) {
            if (!DEBUG_ENABLED) return;
            String timestamp = TIMESTAMP_FORMAT.format(new Date());
            Log.i(componentTag, String.format("[%s] %s", timestamp, message));
        }
        
        protected void logError(String message) {
            if (!DEBUG_ENABLED) return;
            String timestamp = TIMESTAMP_FORMAT.format(new Date());
            Log.e(componentTag, String.format("[%s] %s", timestamp, message));
        }
        
        protected void logWarning(String message) {
            if (!DEBUG_ENABLED) return;
            String timestamp = TIMESTAMP_FORMAT.format(new Date());
            Log.w(componentTag, String.format("[%s] %s", timestamp, message));
        }
        
        protected String formatFileSize(long bytes) {
            if (bytes < 1024) return bytes + "B";
            if (bytes < 1024 * 1024) return String.format(Locale.US, "%.1fKB", bytes / 1024.0);
            return String.format(Locale.US, "%.1fMB", bytes / (1024.0 * 1024.0));
        }
    }
    
    /**
     * Logger for native library operations
     */
    public static class LibraryLogger extends BaseLogger {
        private LibraryLogger() {
            super("Library");
        }
        
        public void loadAttempt(String libraryName) {
            logDebug(String.format("🔄 Loading library: %s", libraryName));
        }
        
        public void loadSuccess(String libraryName, String path, long size) {
            logInfo(String.format("✅ %s loaded successfully (size: %s, path: %s)", 
                libraryName, formatFileSize(size), path));
        }
        
        public void loadFailure(String libraryName, String error) {
            logError(String.format("❌ %s load failed: %s", libraryName, error));
        }
        
        public void loadFailure(String libraryName, Throwable throwable) {
            logError(String.format("❌ %s load failed: %s", libraryName, throwable.getMessage()));
        }
        
        public void nativeMethodCall(String libraryName, String methodName) {
            logDebug(String.format("🔧 Native call: %s.%s()", libraryName, methodName));
        }
        
        public void libraryInfo(String libraryName, String version, String buildType) {
            logInfo(String.format("📋 %s info - Version: %s, Build: %s", 
                libraryName, version, buildType));
        }
    }
    
    /**
     * Logger for script execution operations
     */
    public static class ScriptLogger extends BaseLogger {
        private ScriptLogger() {
            super("Script");
        }
        
        public void executionStart(String scriptName, String path) {
            logDebug(String.format("🔄 Executing script: %s (path: %s)", scriptName, path));
        }
        
        public void executionSuccess(String scriptName, long executionTimeMs) {
            logInfo(String.format("✅ %s executed successfully (time: %dms)", 
                scriptName, executionTimeMs));
        }
        
        public void executionFailure(String scriptName, String error) {
            logError(String.format("❌ %s execution failed: %s", scriptName, error));
        }
        
        public void scriptLoaded(String scriptName, long size, String source) {
            logInfo(String.format("📜 %s loaded (size: %s, source: %s)", 
                scriptName, formatFileSize(size), source));
        }
        
        public void secureScriptOperation(String operation, String scriptId, boolean success) {
            if (success) {
                logInfo(String.format("🔐 SecureScript %s: %s ✅", operation, scriptId));
            } else {
                logError(String.format("🔐 SecureScript %s: %s ❌", operation, scriptId));
            }
        }
    }
    
    /**
     * Logger for OTA file operations
     */
    public static class OTALogger extends BaseLogger {
        private OTALogger() {
            super("OTA");
        }
        
        public void downloadStart(String fileName, String url) {
            logDebug(String.format("🔄 Downloading: %s from %s", fileName, url));
        }
        
        public void downloadProgress(String fileName, int progress, long bytesDownloaded) {
            logDebug(String.format("📥 %s: %d%% (%s)", 
                fileName, progress, formatFileSize(bytesDownloaded)));
        }
        
        public void downloadComplete(String fileName, long size, String checksum) {
            logInfo(String.format("✅ %s downloaded (size: %s, hash: %s)", 
                fileName, formatFileSize(size), checksum != null ? checksum.substring(0, 8) + "..." : "none"));
        }
        
        public void downloadFailure(String fileName, String error) {
            logError(String.format("❌ %s download failed: %s", fileName, error));
        }
        
        public void checksumVerification(String fileName, boolean valid, String expected, String actual) {
            if (valid) {
                logInfo(String.format("🔍 %s checksum verified ✅", fileName));
            } else {
                logError(String.format("🔍 %s checksum mismatch ❌ (expected: %s, got: %s)", 
                    fileName, expected, actual));
            }
        }
        
        public void cacheOperation(String operation, String fileName, boolean success) {
            if (success) {
                logInfo(String.format("💾 Cache %s: %s ✅", operation, fileName));
            } else {
                logWarning(String.format("💾 Cache %s: %s ❌", operation, fileName));
            }
        }
        
        public void keyAuthLibrary(String libraryName, String version, boolean cached) {
            logInfo(String.format("🔑 KeyAuth library: %s v%s %s",
                libraryName, version, cached ? "(cached)" : "(downloaded)"));
        }
    }

    /**
     * Logger for injection system operations
     */
    public static class InjectionLogger extends BaseLogger {
        private InjectionLogger() {
            super("Injection");
        }

        public void mundoCoreInit(String version, boolean success) {
            if (success) {
                logInfo(String.format("✅ MundoCore initialized - Version: %s", version));
            } else {
                logError(String.format("❌ MundoCore initialization failed - Version: %s", version));
            }
        }

        public void injectionAttempt(String method, String targetPackage) {
            logDebug(String.format("🔄 %s injection attempt: %s", method, targetPackage));
        }

        public void injectionSuccess(String method, String targetPackage, long durationMs) {
            logInfo(String.format("✅ %s injection successful: %s (time: %dms)",
                method, targetPackage, durationMs));
        }

        public void injectionFailure(String method, String targetPackage, String error) {
            logError(String.format("❌ %s injection failed: %s - %s",
                method, targetPackage, error));
        }

        public void injectionProgress(String method, int progress, String message) {
            logDebug(String.format("📊 %s: %d%% - %s", method, progress, message));
        }

        public void keyAuthStatus(boolean available, String version) {
            if (available) {
                logInfo(String.format("🔑 KeyAuth system available - Version: %s", version));
            } else {
                logWarning("🔑 KeyAuth system not available");
            }
        }

        public void hybridManagerStatus(boolean initialized, String mode) {
            if (initialized) {
                logInfo(String.format("🔀 HybridInjectionManager initialized - Mode: %s", mode));
            } else {
                logWarning("🔀 HybridInjectionManager initialization failed");
            }
        }

        public void nonRootFallback(String reason) {
            logWarning(String.format("🔄 NonRoot fallback triggered: %s", reason));
        }

        public void securityFeature(String feature, boolean enabled) {
            logInfo(String.format("🛡️ Security feature %s: %s",
                feature, enabled ? "enabled" : "disabled"));
        }
    }

    /**
     * Logger for floating overlay operations
     */
    public static class FloatingLogger extends BaseLogger {
        private FloatingLogger() {
            super("FloatingMenu");
        }

        public void overlayStart(String injectionType) {
            logInfo(String.format("🎯 Floating overlay starting for %s injection", injectionType));
        }

        public void overlaySuccess(String injectionType) {
            logInfo(String.format("✅ Floating overlay activated for %s injection", injectionType));
        }

        public void overlayFailure(String injectionType, String error) {
            logError(String.format("❌ Floating overlay failed for %s: %s", injectionType, error));
        }

        public void permissionCheck(boolean granted) {
            if (granted) {
                logInfo("🔓 Overlay permission granted");
            } else {
                logWarning("🔒 Overlay permission not granted");
            }
        }

        public void imguiInit(boolean success) {
            if (success) {
                logInfo("🎨 ImGui initialized successfully");
            } else {
                logError("🎨 ImGui initialization failed");
            }
        }

        public void renderingStart() {
            logDebug("🎬 Floating overlay rendering started");
        }

        public void renderingStop() {
            logDebug("🛑 Floating overlay rendering stopped");
        }

        public void menuInteraction(String action, String component) {
            logDebug(String.format("👆 Menu interaction: %s on %s", action, component));
        }

        public void glesViewStatus(String status, int width, int height) {
            logDebug(String.format("📺 GLES3JNIView %s (%dx%d)", status, width, height));
        }
    }

    /**
     * Utility methods for advanced logging
     */
    public static class Utils {

        /**
         * Log file information
         */
        public static void logFileInfo(String tag, File file) {
            if (!DEBUG_ENABLED) return;

            if (file.exists()) {
                String timestamp = TIMESTAMP_FORMAT.format(new Date());
                Log.d(String.format("🐻[%s]", tag), String.format("[%s] 📁 File: %s (size: %s, modified: %s)",
                    timestamp, file.getName(),
                    formatFileSize(file.length()),
                    new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.US).format(new Date(file.lastModified()))));
            } else {
                String timestamp = TIMESTAMP_FORMAT.format(new Date());
                Log.w(String.format("🐻[%s]", tag), String.format("[%s] 📁 File not found: %s",
                    timestamp, file.getPath()));
            }
        }

        /**
         * Log memory usage
         */
        public static void logMemoryUsage(String tag) {
            if (!DEBUG_ENABLED) return;

            Runtime runtime = Runtime.getRuntime();
            long totalMemory = runtime.totalMemory();
            long freeMemory = runtime.freeMemory();
            long usedMemory = totalMemory - freeMemory;
            long maxMemory = runtime.maxMemory();

            String timestamp = TIMESTAMP_FORMAT.format(new Date());
            Log.d(String.format("🐻[%s]", tag), String.format("[%s] 💾 Memory: %s used / %s total / %s max",
                timestamp, formatFileSize(usedMemory), formatFileSize(totalMemory), formatFileSize(maxMemory)));
        }

        private static String formatFileSize(long bytes) {
            if (bytes < 1024) return bytes + "B";
            if (bytes < 1024 * 1024) return String.format(Locale.US, "%.1fKB", bytes / 1024.0);
            return String.format(Locale.US, "%.1fMB", bytes / (1024.0 * 1024.0));
        }
    }
}
