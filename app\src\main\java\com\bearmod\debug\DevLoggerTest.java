package com.bearmod.debug;

import android.content.Context;
import android.util.Log;
import com.bearmod.BuildConfig;

/**
 * Test class for DevLogger diagnostic system
 * 
 * This class provides methods to test all DevLogger functionality
 * and verify that logging works correctly in debug builds and
 * is completely disabled in release builds.
 * 
 * Usage:
 * DevLoggerTest.runAllTests(context);
 */
public class DevLoggerTest {
    private static final String TAG = "DevLoggerTest";
    
    /**
     * Run comprehensive tests of the DevLogger system
     */
    public static void runAllTests(Context context) {
        Log.i(TAG, "🧪 Starting DevLogger comprehensive test suite");
        Log.i(TAG, "🔧 Build type: " + BuildConfig.BUILD_TYPE + ", Debug enabled: " + BuildConfig.DEBUG);
        
        // Initialize DevLogger
        DevLogger.initialize(context);
        
        // Test all logger components
        testLibraryLogger();
        testScriptLogger();
        testOTALogger();
        testInjectionLogger();
        testFloatingLogger();
        testUtilityMethods();
        
        Log.i(TAG, "✅ DevLogger test suite completed");
        
        if (BuildConfig.DEBUG) {
            Log.i(TAG, "📊 Debug build: All diagnostic logs should be visible above");
        } else {
            Log.i(TAG, "🚫 Release build: No diagnostic logs should be visible (only this test log)");
        }
    }
    
    /**
     * Test library loading diagnostics
     */
    private static void testLibraryLogger() {
        Log.d(TAG, "Testing LibraryLogger...");
        
        // Test successful library loading
        DevLogger.library().loadAttempt("test-library");
        DevLogger.library().loadSuccess("libtest.so", "/system/lib64/libtest.so", 2048576);
        DevLogger.library().libraryInfo("libtest.so", "1.2.3", "Debug");
        DevLogger.library().nativeMethodCall("libtest.so", "testMethod");
        
        // Test library loading failure
        DevLogger.library().loadFailure("libfailed.so", "Library not found");
        DevLogger.library().loadFailure("libexception.so", new RuntimeException("Test exception"));
    }
    
    /**
     * Test script execution diagnostics
     */
    private static void testScriptLogger() {
        Log.d(TAG, "Testing ScriptLogger...");
        
        // Test script execution flow
        DevLogger.script().executionStart("test-script.js", "/data/scripts/test-script.js");
        DevLogger.script().scriptLoaded("test-script.js", 4096, "KeyAuth");
        DevLogger.script().executionSuccess("test-script.js", 150);
        
        // Test script failure
        DevLogger.script().executionFailure("failed-script.js", "Syntax error in script");
        
        // Test SecureScriptManager operations
        DevLogger.script().secureScriptOperation("load", "anti-detection", true);
        DevLogger.script().secureScriptOperation("execute", "bypass-ssl", false);
    }
    
    /**
     * Test OTA file operations diagnostics
     */
    private static void testOTALogger() {
        Log.d(TAG, "Testing OTALogger...");
        
        // Test download flow
        DevLogger.ota().downloadStart("patch.dex", "https://keyauth.win/api/download");
        DevLogger.ota().downloadProgress("patch.dex", 25, 512000);
        DevLogger.ota().downloadProgress("patch.dex", 50, 1024000);
        DevLogger.ota().downloadProgress("patch.dex", 75, 1536000);
        DevLogger.ota().downloadComplete("patch.dex", 2048000, "abc123def456");
        
        // Test checksum verification
        DevLogger.ota().checksumVerification("patch.dex", true, "abc123", "abc123");
        DevLogger.ota().checksumVerification("corrupted.dex", false, "abc123", "def456");
        
        // Test cache operations
        DevLogger.ota().cacheOperation("hit", "cached-lib.so", true);
        DevLogger.ota().cacheOperation("miss", "new-lib.so", false);
        
        // Test KeyAuth library tracking
        DevLogger.ota().keyAuthLibrary("libbearmod.so", "2.0.1", false);
        DevLogger.ota().keyAuthLibrary("libmundo.so", "1.2.3", true);
        
        // Test download failure
        DevLogger.ota().downloadFailure("failed.dex", "Network timeout");
    }
    
    /**
     * Test injection system diagnostics
     */
    private static void testInjectionLogger() {
        Log.d(TAG, "Testing InjectionLogger...");
        
        // Test MundoCore initialization
        DevLogger.injection().mundoCoreInit("1.2.3", true);
        DevLogger.injection().mundoCoreInit("1.2.4", false);
        
        // Test injection flow
        DevLogger.injection().injectionAttempt("KeyAuth", "com.tencent.ig");
        DevLogger.injection().injectionProgress("KeyAuth", 25, "Preparing injection");
        DevLogger.injection().injectionProgress("KeyAuth", 50, "Loading libraries");
        DevLogger.injection().injectionProgress("KeyAuth", 75, "Executing scripts");
        DevLogger.injection().injectionSuccess("KeyAuth", "com.tencent.ig", 2500);
        
        // Test injection failure
        DevLogger.injection().injectionFailure("Hybrid", "com.pubg.krmobile", "Target process not found");
        
        // Test system status
        DevLogger.injection().keyAuthStatus(true, "1.3");
        DevLogger.injection().keyAuthStatus(false, "unknown");
        DevLogger.injection().hybridManagerStatus(true, "Stealth");
        DevLogger.injection().hybridManagerStatus(false, "Failed");
        
        // Test fallback scenarios
        DevLogger.injection().nonRootFallback("KeyAuth unavailable");
        
        // Test security features
        DevLogger.injection().securityFeature("Anti-Hook Protection", true);
        DevLogger.injection().securityFeature("SSL Pinning Bypass", false);
    }
    
    /**
     * Test floating overlay diagnostics
     */
    private static void testFloatingLogger() {
        Log.d(TAG, "Testing FloatingLogger...");
        
        // Test overlay lifecycle
        DevLogger.floating().overlayStart("KeyAuth");
        DevLogger.floating().permissionCheck(true);
        DevLogger.floating().imguiInit(true);
        DevLogger.floating().glesViewStatus("surface created", 1080, 2340);
        DevLogger.floating().glesViewStatus("surface changed", 1080, 2340);
        DevLogger.floating().renderingStart();
        DevLogger.floating().overlaySuccess("KeyAuth");
        
        // Test user interactions
        DevLogger.floating().menuInteraction("click", "ESP Toggle");
        DevLogger.floating().menuInteraction("drag", "Menu Window");
        
        // Test failure scenarios
        DevLogger.floating().permissionCheck(false);
        DevLogger.floating().imguiInit(false);
        DevLogger.floating().overlayFailure("Hybrid", "OpenGL context creation failed");
        
        // Test cleanup
        DevLogger.floating().renderingStop();
    }
    
    /**
     * Test utility methods
     */
    private static void testUtilityMethods() {
        Log.d(TAG, "Testing utility methods...");
        
        // Test memory usage logging
        DevLogger.Utils.logMemoryUsage("TestComponent");
        
        // Test file info logging (create a temporary file for testing)
        try {
            java.io.File testFile = new java.io.File("/data/data/com.bearmod/test.tmp");
            if (testFile.createNewFile()) {
                DevLogger.Utils.logFileInfo("TestComponent", testFile);
                testFile.delete();
            }
            
            // Test with non-existent file
            java.io.File nonExistentFile = new java.io.File("/data/data/com.bearmod/nonexistent.tmp");
            DevLogger.Utils.logFileInfo("TestComponent", nonExistentFile);
            
        } catch (Exception e) {
            Log.w(TAG, "Could not test file operations: " + e.getMessage());
        }
    }
    
    /**
     * Quick test method for basic functionality verification
     */
    public static void quickTest(Context context) {
        Log.i(TAG, "🚀 DevLogger quick test");
        
        DevLogger.initialize(context);
        
        // Test one method from each logger
        DevLogger.library().loadSuccess("libtest.so", "/test/path", 1024);
        DevLogger.script().executionSuccess("test.js", 100);
        DevLogger.ota().downloadComplete("test.dex", 2048, "hash123");
        DevLogger.injection().mundoCoreInit("1.0.0", true);
        DevLogger.floating().overlaySuccess("Test");
        
        Log.i(TAG, "✅ DevLogger quick test completed");
    }
}
