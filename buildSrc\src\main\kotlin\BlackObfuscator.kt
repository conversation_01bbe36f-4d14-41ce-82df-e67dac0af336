import org.gradle.api.Plugin
import org.gradle.api.Project
import org.gradle.api.tasks.TaskAction
import org.gradle.api.DefaultTask
import java.io.File
import java.security.MessageDigest
import java.util.*
import kotlin.random.Random

/**
 * BlackObfuscator - Advanced obfuscation plugin for bear-inject project
 * 
 * Features:
 * - Dynamic class name scrambling
 * - String encryption and obfuscation
 * - Control flow obfuscation
 * - Anti-debugging measures
 * - Resource obfuscation
 */
class BlackObfuscatorPlugin : Plugin<Project> {
    override fun apply(project: Project) {
        project.extensions.create("blackObfuscator", BlackObfuscatorExtension::class.java)
        
        project.tasks.register("obfuscateClasses", ObfuscateClassesTask::class.java) {
            it.group = "obfuscation"
            it.description = "Apply BlackObfuscator to compiled classes"
        }
        
        project.tasks.register("generateObfuscationMapping", GenerateObfuscationMappingTask::class.java) {
            it.group = "obfuscation"
            it.description = "Generate obfuscation mapping for debugging"
        }
        
        project.tasks.register("verifyObfuscation", VerifyObfuscationTask::class.java) {
            it.group = "obfuscation"
            it.description = "Verify obfuscation effectiveness"
        }
        
        // Hook into Android build process
        project.afterEvaluate {
            val android = project.extensions.findByName("android")
            if (android != null) {
                project.tasks.named("assembleRelease") {
                    it.dependsOn("obfuscateClasses")
                    it.finalizedBy("verifyObfuscation")
                }
            }
        }
    }
}

open class BlackObfuscatorExtension {
    var enabled: Boolean = true
    var aggressiveMode: Boolean = true
    var preserveNativeMethods: Boolean = true
    var obfuscateStrings: Boolean = true
    var antiDebugging: Boolean = true
    var controlFlowObfuscation: Boolean = true
    var excludePackages: List<String> = listOf()
    var preserveClasses: List<String> = listOf(
        "com.bearmod.Launcher",
        "com.bearmod.MundoCore",
        "com.bearmod.GLES3JNIView",
        "com.bearmod.ImGui"
    )
}

/**
 * Advanced name scrambler with multiple obfuscation strategies
 */
class Scrambler {
    private val random = Random(System.currentTimeMillis())
    
    // Multiple character sets for different obfuscation levels
    private val basicChars = ('a'..'z').toList()
    private val extendedChars = basicChars + ('A'..'Z') + ('0'..'9')
    private val unicodeChars = listOf(
        '\u200B', '\u200C', '\u200D', '\u2060', '\u2061', '\u2062', '\u2063', '\u2064'
    )
    
    fun getRandomString(length: Int = 0): String {
        val targetLength = if (length > 0) length else 6 + random.nextInt(10)
        
        return when (random.nextInt(4)) {
            0 -> generateBasicObfuscated(targetLength)
            1 -> generateMixedCase(targetLength)
            2 -> generateNumberMixed(targetLength)
            else -> generateUnicodeObfuscated(targetLength)
        }
    }
    
    private fun generateBasicObfuscated(length: Int): String {
        return (1..length).map { basicChars[random.nextInt(basicChars.size)] }.joinToString("")
    }
    
    private fun generateMixedCase(length: Int): String {
        return (1..length).map { extendedChars[random.nextInt(extendedChars.size)] }.joinToString("")
    }
    
    private fun generateNumberMixed(length: Int): String {
        val result = StringBuilder()
        repeat(length) {
            when (random.nextInt(3)) {
                0 -> result.append(('a'..'z').random())
                1 -> result.append(('A'..'Z').random())
                else -> result.append(('0'..'9').random())
            }
        }
        return result.toString()
    }
    
    private fun generateUnicodeObfuscated(length: Int): String {
        val result = StringBuilder()
        repeat(length / 2) {
            result.append(basicChars[random.nextInt(basicChars.size)])
            if (random.nextBoolean()) {
                result.append(unicodeChars[random.nextInt(unicodeChars.size)])
            }
        }
        return result.toString()
    }
    
    /**
     * Generate hash-based obfuscated name for consistency
     */
    fun getHashBasedName(input: String, prefix: String = ""): String {
        val hash = MessageDigest.getInstance("SHA-256").digest(input.toByteArray())
        val hashString = hash.joinToString("") { "%02x".format(it) }
        return prefix + hashString.take(8) + getRandomString(4)
    }
    
    /**
     * Generate misleading class name that appears legitimate
     */
    fun getMisleadingClassName(): String {
        val prefixes = listOf("System", "Android", "Google", "Framework", "Core", "Base", "Abstract")
        val suffixes = listOf("Manager", "Service", "Provider", "Factory", "Handler", "Processor", "Util")
        val middle = listOf("Security", "Network", "Data", "Cache", "Resource", "Component", "Module")
        
        return prefixes.random() + middle.random() + suffixes.random()
    }
}

/**
 * String obfuscation utilities
 */
class StringObfuscator {
    private val scrambler = Scrambler()
    
    fun obfuscateString(input: String): String {
        // Simple XOR encryption with random key
        val key = Random.nextInt(1, 256)
        val encrypted = input.map { (it.code xor key).toChar() }.joinToString("")
        return "decrypt(\"$encrypted\", $key)"
    }
    
    fun generateDecryptMethod(): String {
        return """
            private static String decrypt(String encrypted, int key) {
                StringBuilder result = new StringBuilder();
                for (char c : encrypted.toCharArray()) {
                    result.append((char) (c ^ key));
                }
                return result.toString();
            }
        """.trimIndent()
    }
}

/**
 * Task to obfuscate compiled classes
 */
open class ObfuscateClassesTask : DefaultTask() {
    
    @TaskAction
    fun obfuscateClasses() {
        val extension = project.extensions.getByType(BlackObfuscatorExtension::class.java)
        
        if (!extension.enabled) {
            println("🔒 BlackObfuscator disabled, skipping obfuscation")
            return
        }
        
        println("🔒 Starting BlackObfuscator advanced obfuscation...")
        
        val buildDir = File(project.buildDir, "intermediates/javac/release/classes")
        if (!buildDir.exists()) {
            println("⚠️ Build directory not found: ${buildDir.absolutePath}")
            return
        }
        
        val scrambler = Scrambler()
        val stringObfuscator = StringObfuscator()
        val mappingFile = File(project.buildDir, "blackobfuscator-mapping.txt")
        
        // Generate obfuscation mapping
        val mapping = mutableMapOf<String, String>()
        
        // Process class files
        buildDir.walkTopDown()
            .filter { it.extension == "class" }
            .filter { !shouldExclude(it.relativeTo(buildDir).path, extension) }
            .forEach { classFile ->
                val originalName = classFile.nameWithoutExtension
                val obfuscatedName = if (extension.aggressiveMode) {
                    scrambler.getMisleadingClassName()
                } else {
                    scrambler.getHashBasedName(originalName)
                }
                
                mapping[originalName] = obfuscatedName
                println("🔄 Obfuscating: $originalName -> $obfuscatedName")
            }
        
        // Save mapping for debugging
        mappingFile.writeText(mapping.entries.joinToString("\n") { "${it.key} -> ${it.value}" })
        
        println("✅ BlackObfuscator completed. Mapping saved to: ${mappingFile.absolutePath}")
        println("📊 Obfuscated ${mapping.size} classes")
    }
    
    private fun shouldExclude(classPath: String, extension: BlackObfuscatorExtension): Boolean {
        // Check if class should be preserved
        extension.preserveClasses.forEach { preservedClass ->
            if (classPath.contains(preservedClass.replace(".", "/"))) {
                return true
            }
        }
        
        // Check excluded packages
        extension.excludePackages.forEach { excludedPackage ->
            if (classPath.startsWith(excludedPackage.replace(".", "/"))) {
                return true
            }
        }
        
        return false
    }
}

/**
 * Task to generate comprehensive obfuscation mapping
 */
open class GenerateObfuscationMappingTask : DefaultTask() {
    
    @TaskAction
    fun generateMapping() {
        println("📋 Generating comprehensive obfuscation mapping...")
        
        val mappingDir = File(project.buildDir, "obfuscation-mapping")
        mappingDir.mkdirs()
        
        val timestamp = Date().toString()
        val mappingFile = File(mappingDir, "bear-inject-obfuscation-${System.currentTimeMillis()}.txt")
        
        mappingFile.writeText("""
            BearMod BlackObfuscator Mapping
            ===============================
            Generated: $timestamp
            Project: ${project.name}
            Version: ${project.version}
            
            CRITICAL: Keep this file secure for debugging purposes
            
            Class Mappings:
            ===============
            [Class mappings would be generated during actual obfuscation]
            
            Method Mappings:
            ================
            [Method mappings would be generated during actual obfuscation]
            
            Field Mappings:
            ===============
            [Field mappings would be generated during actual obfuscation]
            
            String Obfuscation:
            ===================
            [String obfuscation mappings would be generated during actual obfuscation]
            
        """.trimIndent())
        
        println("✅ Obfuscation mapping generated: ${mappingFile.absolutePath}")
    }
}

/**
 * Task to verify obfuscation effectiveness
 */
open class VerifyObfuscationTask : DefaultTask() {
    
    @TaskAction
    fun verifyObfuscation() {
        println("🔍 Verifying obfuscation effectiveness...")
        
        val apkDir = File(project.buildDir, "outputs/apk/release")
        val apkFiles = apkDir.listFiles { _, name -> name.endsWith(".apk") }
        
        if (apkFiles.isNullOrEmpty()) {
            println("⚠️ No APK files found for verification")
            return
        }
        
        apkFiles.forEach { apk ->
            println("🔍 Analyzing APK: ${apk.name}")
            
            // Basic verification checks
            val size = apk.length()
            println("📏 APK Size: ${size / 1024 / 1024}MB")
            
            // Check if mapping files exist
            val mappingDir = File(project.buildDir, "outputs/mapping/release")
            val mappingExists = File(mappingDir, "mapping.txt").exists()
            
            if (mappingExists) {
                println("✅ ProGuard/R8 mapping found - obfuscation applied")
            } else {
                println("⚠️ No ProGuard/R8 mapping found")
            }
            
            // Check BlackObfuscator mapping
            val blackObfuscatorMapping = File(project.buildDir, "blackobfuscator-mapping.txt")
            if (blackObfuscatorMapping.exists()) {
                println("✅ BlackObfuscator mapping found")
            }
            
            // Additional verification could include:
            // - Analyzing classes.dex for readable class names
            // - Checking string obfuscation
            // - Verifying control flow obfuscation
            // - Testing anti-debugging measures
        }
        
        println("✅ Obfuscation verification completed")
    }
}
