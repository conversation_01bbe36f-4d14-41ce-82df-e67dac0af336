# BearMod Automated Build System Guide

## Overview

This guide covers the comprehensive automated build system for the bear-inject project, including obfuscation, APK naming, mapping file management, and security verification.

## 🏗️ Build System Components

### 1. **Automated APK Naming**
- Format: `{applicationId}-{buildType}-v{version}-{timestamp}.apk`
- Example: `com.bearmod-release-v2.0-20241206_143022.apk`
- Automatic timestamp generation for unique builds

### 2. **Mapping File Archival**
- Automatic copying of ProGuard/R8 mapping files
- Timestamped mapping files for version tracking
- BlackObfuscator mapping generation
- Configuration and usage file archival

### 3. **Code Obfuscation**
- **ProGuard/R8**: Enhanced configuration with aggressive optimization
- **BlackObfuscator**: Custom obfuscation plugin with advanced features
- **DevLogger Removal**: Complete removal from release builds
- **String Obfuscation**: Sensitive string encryption

### 4. **Build Archival**
- Automatic build directory creation with timestamps
- APK, mapping files, and build reports archived together
- Old build cleanup (30+ days)
- Comprehensive build information tracking

## 🔧 Configuration Files

### `app/build.gradle.kts`
```kotlin
// Enhanced release configuration
release {
    isMinifyEnabled = true
    isShrinkResources = true
    proguardFiles(
        getDefaultProguardFile("proguard-android-optimize.txt"), 
        "proguard-rules.pro",
        "proguard-obfuscation.pro"
    )
}

// BlackObfuscator configuration
blackObfuscator {
    enabled = true
    aggressiveMode = true
    preserveNativeMethods = true
    obfuscateStrings = true
    antiDebugging = true
    controlFlowObfuscation = true
}
```

### `app/proguard-rules.pro`
- Basic ProGuard configuration
- Native method preservation
- Dependency optimization
- DevLogger removal rules

### `app/proguard-obfuscation.pro`
- Advanced obfuscation settings
- Complete DevLogger elimination
- Anti-reverse engineering measures
- String obfuscation preparation

### `app/obfuscation-dictionary.txt`
- Custom obfuscation dictionary
- Misleading class and method names
- Enhanced name scrambling

## 🚀 Usage Instructions

### Quick Build (Recommended)
```bash
# Clean build with full obfuscation
./scripts/build-release.sh clean

# Regular build
./scripts/build-release.sh
```

### Manual Gradle Build
```bash
# Clean and build release
./gradlew clean assembleRelease

# Build with R8 full mode
./gradlew assembleRelease -Pandroid.enableR8.fullMode=true
```

### Verification
```bash
# Verify obfuscation effectiveness
python3 scripts/verify-obfuscation.py path/to/your.apk
```

## 📊 Obfuscation Features

### ProGuard/R8 Obfuscation
- **Class name obfuscation**: All non-essential classes renamed
- **Method obfuscation**: Method names scrambled
- **Field obfuscation**: Field names obfuscated
- **Code optimization**: Dead code elimination and optimization
- **Resource shrinking**: Unused resources removed

### BlackObfuscator Features
- **Dynamic name generation**: Hash-based and random name generation
- **Misleading class names**: Names that appear legitimate
- **String encryption**: XOR encryption with random keys
- **Control flow obfuscation**: Code flow scrambling
- **Anti-debugging measures**: Runtime protection

### DevLogger Removal
```java
// Debug builds: Full logging
DevLogger.library().loadSuccess("lib.so", path, size);

// Release builds: Completely removed by ProGuard
// No trace of DevLogger in final APK
```

## 🔍 Verification Process

### Automated Verification
The build system automatically verifies:
- ✅ APK size optimization
- ✅ DevLogger complete removal
- ✅ Class name obfuscation
- ✅ Sensitive string removal
- ✅ Native library preservation
- ✅ Mapping file generation

### Manual Verification
```bash
# Check APK contents
unzip -l your.apk

# Analyze classes.dex (requires dex2jar)
dex2jar your.apk
jar -tf your-dex2jar.jar | grep -i bearmod

# Check for DevLogger traces
strings classes.dex | grep -i devlogger
```

## 📁 Build Archive Structure

```
build-archive/
├── release-20241206_143022/
│   ├── com.bearmod-release-v2.0-20241206_143022.apk
│   ├── mapping-20241206_143022.txt
│   ├── blackobfuscator-mapping-20241206_143022.txt
│   ├── r8-configuration-20241206_143022.txt
│   ├── seeds-20241206_143022.txt
│   ├── usage-20241206_143022.txt
│   └── build-report-20241206_143022.txt
└── debug-20241206_140015/
    ├── com.bearmod-debug-v2.0-20241206_140015.apk
    └── build-info.txt
```

## 🛡️ Security Measures

### Release Build Security
- **Complete obfuscation**: Classes.dex unreadable
- **DevLogger removal**: No debug traces
- **String encryption**: Sensitive strings protected
- **Resource optimization**: Minimal attack surface
- **Anti-debugging**: Runtime protection measures

### Debug Build Features
- **Full DevLogger**: Comprehensive diagnostic logging
- **Readable code**: No obfuscation for debugging
- **Debug symbols**: Full debugging information
- **Performance monitoring**: Memory and execution tracking

## 🔧 Troubleshooting

### Common Issues

1. **Build fails with obfuscation errors**
   ```bash
   # Check ProGuard rules
   ./gradlew assembleRelease --info
   
   # Disable BlackObfuscator temporarily
   blackObfuscator { enabled = false }
   ```

2. **Native methods not working**
   ```bash
   # Verify native method preservation
   grep -r "native" app/proguard-*.pro
   ```

3. **DevLogger still present in release**
   ```bash
   # Check ProGuard configuration
   grep -A 10 "DevLogger" app/proguard-*.pro
   ```

4. **APK too large**
   ```bash
   # Enable resource shrinking
   isShrinkResources = true
   
   # Check unused resources
   ./gradlew assembleRelease --info | grep "Removed unused"
   ```

### Verification Commands
```bash
# Check obfuscation effectiveness
python3 scripts/verify-obfuscation.py build-archive/latest/your.apk

# Analyze APK size
./gradlew assembleRelease
ls -lh app/build/outputs/apk/release/

# Check mapping files
ls -la app/build/outputs/mapping/release/
```

## 📈 Performance Metrics

### Target Metrics
- **APK Size**: < 20MB (optimized from ~70MB)
- **Obfuscation Score**: > 80/100
- **DevLogger Removal**: 100% (zero traces)
- **Build Time**: < 5 minutes
- **Native Library Preservation**: 100%

### Monitoring
```bash
# Build time tracking
time ./scripts/build-release.sh

# APK size comparison
du -h app/build/outputs/apk/*/com.bearmod-*.apk

# Obfuscation verification
python3 scripts/verify-obfuscation.py your.apk
```

## 🔄 Continuous Integration

### GitHub Actions Integration
```yaml
- name: Build Release APK
  run: ./scripts/build-release.sh clean

- name: Verify Obfuscation
  run: python3 scripts/verify-obfuscation.py app/build/outputs/apk/release/*.apk

- name: Archive Build
  uses: actions/upload-artifact@v3
  with:
    name: bear-inject-release
    path: build-archive/
```

## 📝 Best Practices

1. **Always verify obfuscation** before distributing release builds
2. **Keep mapping files secure** for debugging purposes
3. **Test on multiple devices** after obfuscation
4. **Monitor APK size** to ensure optimization is working
5. **Verify DevLogger removal** in every release build
6. **Use clean builds** for final releases
7. **Archive all builds** with timestamps for tracking

## 🆘 Support

For issues with the build system:
1. Check the troubleshooting section above
2. Review build logs for specific errors
3. Verify all configuration files are present
4. Test with a clean build environment
5. Check that all required tools are installed
