# DevLogger Diagnostic System

## Overview

The DevLogger is a comprehensive debug-only diagnostic logging system for the bear-inject project. It provides detailed tracking of all critical operations including native library loading, script execution, OTA downloads, injection processes, and floating overlay management.

## Key Features

- **Debug-only operation**: Uses `BuildConfig.DEBUG` checks to ensure zero overhead in release builds
- **Comprehensive coverage**: Tracks all critical file operations and system components
- **Consistent formatting**: Uses bear emoji prefix (🐻) and standardized log formats
- **Component-specific loggers**: Separate loggers for different system components
- **Thread-safe operations**: Safe for use across multiple threads
- **File size and checksum tracking**: Detailed information about downloaded files
- **Performance monitoring**: Execution time tracking for operations

## Architecture

### Core Components

1. **DevLogger** - Main class with static access methods
2. **LibraryLogger** - Native library loading diagnostics
3. **ScriptLogger** - Script execution monitoring
4. **OTALogger** - File download and caching operations
5. **InjectionLogger** - Injection system diagnostics
6. **FloatingLogger** - Floating overlay operations
7. **Utils** - Utility methods for advanced logging

### Log Format

All logs follow a consistent format:
```
🐻[ComponentName] [HH:mm:ss.SSS] [Status] Message
```

Example outputs:
```
🐻[Library] [14:32:15.123] ✅ libbearmod.so loaded successfully (size: 2.1MB, path: /system/lib64/libbearmod.so)
🐻[Script] [14:32:16.456] 🔄 Executing script: anti-detection.js (path: /data/scripts/anti-detection.js)
🐻[OTA] [14:32:17.789] 📥 patch.dex: 75% (1.5MB)
🐻[Injection] [14:32:18.012] ✅ KeyAuth injection successful: com.tencent.ig (time: 2500ms)
🐻[FloatingMenu] [14:32:19.345] 🎯 Floating overlay starting for KeyAuth injection
```

## Usage Guide

### Initialization

Initialize DevLogger once in your Application or MainActivity:

```java
// In MainActivity.onCreate() or Application.onCreate()
DevLogger.initialize(this);
```

### Library Loading Diagnostics

```java
// Before loading library
DevLogger.library().loadAttempt("bearmod");

try {
    System.loadLibrary("bearmod");
    // After successful load
    DevLogger.library().loadSuccess("libbearmod.so", "/system/lib64/libbearmod.so", fileSize);
    DevLogger.library().libraryInfo("libbearmod.so", "2.0.1", "Release");
} catch (UnsatisfiedLinkError e) {
    DevLogger.library().loadFailure("libbearmod.so", e);
}

// Native method calls
DevLogger.library().nativeMethodCall("libbearmod.so", "initializeCore");
```

### Script Execution Monitoring

```java
// Script loading and execution
DevLogger.script().executionStart("anti-detection.js", "/data/scripts/anti-detection.js");
DevLogger.script().scriptLoaded("anti-detection.js", scriptContent.length(), "KeyAuth");

long startTime = System.currentTimeMillis();
boolean success = executeScript(scriptContent);
long executionTime = System.currentTimeMillis() - startTime;

if (success) {
    DevLogger.script().executionSuccess("anti-detection.js", executionTime);
} else {
    DevLogger.script().executionFailure("anti-detection.js", "Script execution failed");
}

// SecureScriptManager operations
DevLogger.script().secureScriptOperation("load", scriptId, success);
```

### OTA File Operations

```java
// Download tracking
DevLogger.ota().downloadStart("patch.dex", "https://keyauth.win/api/download");

// Progress updates
DevLogger.ota().downloadProgress("patch.dex", progress, bytesDownloaded);

// Completion
DevLogger.ota().downloadComplete("patch.dex", fileSize, checksum);

// Checksum verification
DevLogger.ota().checksumVerification("patch.dex", isValid, expectedHash, actualHash);

// Cache operations
DevLogger.ota().cacheOperation("hit", "cached-lib.so", true);
DevLogger.ota().keyAuthLibrary("libbearmod.so", "2.0.1", wasCached);

// Failures
DevLogger.ota().downloadFailure("failed.dex", errorMessage);
```

### Injection System Diagnostics

```java
// MundoCore initialization
DevLogger.injection().mundoCoreInit(version, success);

// Injection flow
DevLogger.injection().injectionAttempt("KeyAuth", targetPackage);
DevLogger.injection().injectionProgress("KeyAuth", progress, message);
DevLogger.injection().injectionSuccess("KeyAuth", targetPackage, durationMs);
DevLogger.injection().injectionFailure("KeyAuth", targetPackage, error);

// System status
DevLogger.injection().keyAuthStatus(available, version);
DevLogger.injection().hybridManagerStatus(initialized, mode);
DevLogger.injection().nonRootFallback(reason);
DevLogger.injection().securityFeature("Anti-Hook Protection", enabled);
```

### Floating Overlay Diagnostics

```java
// Overlay lifecycle
DevLogger.floating().overlayStart(injectionType);
DevLogger.floating().permissionCheck(granted);
DevLogger.floating().imguiInit(success);
DevLogger.floating().overlaySuccess(injectionType);

// GLES operations
DevLogger.floating().glesViewStatus("surface created", width, height);
DevLogger.floating().renderingStart();

// User interactions
DevLogger.floating().menuInteraction("click", "ESP Toggle");

// Failures
DevLogger.floating().overlayFailure(injectionType, error);
DevLogger.floating().renderingStop();
```

### Utility Methods

```java
// Memory usage tracking
DevLogger.Utils.logMemoryUsage("ComponentName");

// File information
DevLogger.Utils.logFileInfo("ComponentName", file);
```

## Integration Points

The DevLogger is integrated into the following components:

### Core Classes
- `MainActivity.java` - Initialization and injection flow
- `MundoCore.java` - Native library loading and initialization
- `NonRootManager.java` - Native library loading

### Script Management
- `SecureScriptManager.java` - Script loading and execution
- `FridaPatchManager.java` - Frida script operations
- `JSEnginePatchManager.java` - JavaScript engine operations

### OTA and Downloads
- `OTAUpdateManager.java` - OTA file operations
- `SimpleLicenseVerifier.java` - KeyAuth downloads
- `KeyAuthInjectionManager.java` - Library management

### Injection System
- `KeyAuthInjectionManager.java` - KeyAuth injection flow
- `HybridInjectionManager.java` - Hybrid injection system
- `NonRootPatchManager.java` - Non-root patching

### Floating Overlay
- `ImGui.java` - Floating overlay management
- `GLES3JNIView.java` - OpenGL surface operations

## Testing

### Comprehensive Test Suite

Run the full test suite to verify all logging functionality:

```java
// Run all tests
DevLoggerTest.runAllTests(context);

// Quick functionality test
DevLoggerTest.quickTest(context);
```

### Manual Testing

1. **Debug Build Testing**:
   - Build in debug mode
   - Check logcat for diagnostic messages with 🐻 prefix
   - Verify all operations are logged with timestamps

2. **Release Build Testing**:
   - Build in release mode
   - Verify NO diagnostic messages appear in logcat
   - Confirm zero performance overhead

### Expected Log Output (Debug Build)

```
🐻[BearInject] [14:30:00.123] 🚀 DevLogger initialized - Debug mode active
🐻[BearInject] [14:30:00.124] 📱 App: com.bearmod v1.3 (DEBUG)
🐻[Library] [14:30:01.234] 🔄 Loading library: bearmod
🐻[Library] [14:30:01.456] ✅ libbearmod.so loaded successfully (size: 2.1MB, path: system library path)
🐻[Injection] [14:30:05.789] ✅ MundoCore initialized - Version: 1.2.3
🐻[Injection] [14:30:10.012] 🔑 KeyAuth system available - Version: KeyAuth
🐻[Injection] [14:30:10.345] 🔄 KeyAuth injection attempt: com.tencent.ig
🐻[FloatingMenu] [14:30:15.678] 🎯 Floating overlay starting for KeyAuth injection
🐻[FloatingMenu] [14:30:15.901] 🔓 Overlay permission granted
🐻[FloatingMenu] [14:30:16.234] 🎨 ImGui initialized successfully
🐻[FloatingMenu] [14:30:16.567] ✅ Floating overlay activated for KeyAuth injection
```

## Performance Considerations

- **Zero overhead in release builds**: All logging calls are completely removed by ProGuard
- **Minimal overhead in debug builds**: Logging operations are optimized for performance
- **Thread-safe operations**: Safe for concurrent use across multiple threads
- **Memory efficient**: Uses string formatting only when logging is enabled

## Build Configuration

The system automatically detects build type using `BuildConfig.DEBUG`:

```java
// Debug builds: Full logging enabled
if (BuildConfig.DEBUG) {
    // All diagnostic logging active
}

// Release builds: All logging disabled
// ProGuard removes all DevLogger calls
```

## Troubleshooting

### Common Issues

1. **No logs appearing in debug build**:
   - Verify `BuildConfig.DEBUG` is true
   - Check logcat filters for 🐻 prefix
   - Ensure DevLogger.initialize() was called

2. **Logs appearing in release build**:
   - Check ProGuard configuration
   - Verify release build configuration
   - Review build.gradle settings

3. **Performance impact**:
   - DevLogger should have zero impact in release builds
   - Debug builds may have minimal logging overhead
   - Use DevLoggerTest to verify performance

### Debug Commands

```bash
# Filter DevLogger messages
adb logcat | grep "🐻"

# Filter specific component
adb logcat | grep "🐻\[Library\]"

# Clear logcat and monitor
adb logcat -c && adb logcat | grep "🐻"
```

## Future Enhancements

- Optional log file output for debugging
- Network logging for remote diagnostics
- Performance metrics collection
- Custom log level filtering
- Integration with crash reporting systems
